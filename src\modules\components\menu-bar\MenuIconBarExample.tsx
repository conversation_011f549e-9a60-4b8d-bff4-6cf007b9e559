import React, { useState } from 'react';
import { Card, Typography } from '@/shared/components/common';
import MenuIconBar, { ColumnVisibility, ColumnLabelMap } from './MenuIconBar';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { useTranslation } from 'react-i18next';

/**
 * Component ví dụ sử dụng MenuIconBar nâng cao
 */
const MenuIconBarExample: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([
    { id: 'all', label: 'Tất cả', visible: true },
    { id: 'code', label: 'Mã', visible: true },
    { id: 'name', label: 'Tên', visible: true },
    { id: 'description', label: '<PERSON>ô tả', visible: true },
    { id: 'price', label: 'Giá', visible: true },
    { id: 'status', label: 'Trạng thái', visible: true },
    { id: 'createdAt', label: 'Ngày tạo', visible: true },
    { id: 'actions', label: 'Thao tác', visible: true },
  ]);

  // Map nhãn cho các cột (sử dụng key translation)
  const columnLabelMap: ColumnLabelMap = {
    code: 'common.code',
    name: 'common.name',
    description: 'common.description',
    price: 'common.price',
    status: 'common.status',
    createdAt: 'common.createdAt',
    actions: 'common.actions',
  };

  // Các mục lọc
  const filterItems: ModernMenuItem[] = [
    {
      id: 'all',
      label: t('common.all'),
      icon: 'layers',
      onClick: () => console.log('Lọc: Tất cả'),
    },
    {
      id: 'active',
      label: t('common.active'),
      icon: 'check-circle',
      onClick: () => console.log('Lọc: Đang hoạt động'),
    },
    {
      id: 'inactive',
      label: t('common.inactive'),
      icon: 'x-circle',
      onClick: () => console.log('Lọc: Không hoạt động'),
    },
    {
      id: 'pending',
      label: t('common.pending'),
      icon: 'clock',
      onClick: () => console.log('Lọc: Đang chờ'),
    },
  ];

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    console.log('Tìm kiếm:', term);
  };

  // Xử lý thêm mới
  const handleAdd = () => {
    console.log('Thêm mới');
  };

  // Xử lý thay đổi khoảng thời gian
  const handleDateRangeChange = (range: [Date | null, Date | null]) => {
    setDateRange(range);
    console.log('Khoảng thời gian:', range);
  };

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = (columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
    console.log('Hiển thị cột:', columns);
  };

  return (
    <div className="p-4">
      <Typography variant="h4" className="mb-4">
        Ví dụ sử dụng MenuIconBar nâng cao
      </Typography>

      <Card className="mb-6">
        <div className="p-4">
          <Typography variant="h5" className="mb-4">
            MenuIconBar với đầy đủ tính năng
          </Typography>

          <MenuIconBar
            onSearch={handleSearch}
            onAdd={handleAdd}
            onDateRangeChange={handleDateRangeChange}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            columns={visibleColumns}
            columnLabelMap={columnLabelMap}
            showDateFilter={true}
            showColumnFilter={true}
          />

          <div className="mt-6 space-y-4">
            <div>
              <Typography variant="subtitle1">Kết quả tìm kiếm:</Typography>
              <Typography variant="body2">{searchTerm || '(Chưa có)'}</Typography>
            </div>

            <div>
              <Typography variant="subtitle1">Khoảng thời gian:</Typography>
              <Typography variant="body2">
                {dateRange[0] ? dateRange[0].toLocaleDateString() : '(Không có)'} -{' '}
                {dateRange[1] ? dateRange[1].toLocaleDateString() : '(Không có)'}
              </Typography>
            </div>

            <div>
              <Typography variant="subtitle1">Cột hiển thị:</Typography>
              <div className="flex flex-wrap gap-2">
                {visibleColumns
                  .filter(col => col.id !== 'all' && col.visible)
                  .map(col => (
                    <div
                      key={col.id}
                      className="px-2 py-1 bg-primary/10 dark:bg-primary/20 rounded-md text-sm"
                    >
                      {t(columnLabelMap[col.id] || col.label, col.label)}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card className="mb-6">
        <div className="p-4">
          <Typography variant="h5" className="mb-4">
            MenuIconBar chỉ với tìm kiếm và thêm mới
          </Typography>

          <MenuIconBar
            onSearch={handleSearch}
            onAdd={handleAdd}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>
      </Card>

      <Card>
        <div className="p-4">
          <Typography variant="h5" className="mb-4">
            MenuIconBar với lọc và tìm kiếm (không có thêm mới)
          </Typography>

          <MenuIconBar
            onSearch={handleSearch}
            items={filterItems}
            showDateFilter={true}
            showColumnFilter={false}
          />
        </div>
      </Card>
    </div>
  );
};

export default MenuIconBarExample;
