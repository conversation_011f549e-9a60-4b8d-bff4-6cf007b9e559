import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { Controller, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Card,
  Form,
  FormGrid,
  Input,
  Button,
  RadioGroup,
  FormArray,
  Select,
  Icon,
  Grid,
  ScrollArea,
  Accordion,
  Chip,
} from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import { ImageUploader } from '../components';

// Định nghĩa schema cho form
const productSchema = z.object({
  name: z.string().min(1, 'Vui lòng nhập tên sản phẩm'),
  priceType: z.enum(['có', 'không', 'khác']),
  regularPrice: z.string().optional(),
  salePrice: z.string().optional(),
  priceNote: z.string().optional(), // Thêm trường priceNote cho loại gi<PERSON> "khác"
  brand: z.string().optional(),
  url: z.string().optional(),
  description: z.string().optional(),
  attributes: z.array(
    z.object({
      name: z.string().min(1, 'Vui lòng nhập tên thuộc tính'),
      type: z.string().min(1, 'Vui lòng chọn kiểu dữ liệu'),
      value: z.string().optional(),
    })
  ),
});

// Kiểu dữ liệu cho form
type ProductFormValues = z.infer<typeof productSchema>;

// Kiểu dữ liệu cho thuộc tính
const attributeTypes = [
  { label: 'Văn bản', value: 'text' },
  { label: 'Số', value: 'number' },
  { label: 'Ngày tháng', value: 'date' },
  { label: 'Có/Không', value: 'boolean' },
  { label: 'Danh sách', value: 'list' },
];

/**
 * Trang tạo sản phẩm mới
 */
interface ProductImage {
  id: string;
  type: 'image' | 'url' | 'video';
  src: string;
  isCover?: boolean;
}

const CreateProductPage: React.FC = () => {
  const { t } = useTranslation('business');
  useTheme();
  const [uploadType, setUploadType] = useState<'image' | 'url' | 'video'>('image');
  const [imageUrl, setImageUrl] = useState<string>('');
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [productImages, setProductImages] = useState<ProductImage[]>(() => {
    // Lấy dữ liệu từ localStorage nếu có
    const savedImages = localStorage.getItem('business_product_images');
    return savedImages ? JSON.parse(savedImages) : [];
  });
  const [tempImage, setTempImage] = useState<string | null>(null);

  // Lưu productImages vào localStorage mỗi khi thay đổi
  useEffect(() => {
    localStorage.setItem('business_product_images', JSON.stringify(productImages));
  }, [productImages]);

  // Giá trị mặc định cho form
  const defaultValues: ProductFormValues = {
    name: '',
    priceType: 'có',
    regularPrice: '',
    salePrice: '',
    priceNote: '',
    brand: '',
    url: '',
    description: '',
    attributes: [],
  };

  // Xử lý khi submit form
  const handleSubmit = (values: ProductFormValues) => {
    console.log('Form values:', values);
    console.log('Product images:', productImages);

    // Xử lý tạo sản phẩm

    // Sau khi tạo sản phẩm thành công, xóa dữ liệu trong localStorage
    localStorage.removeItem('business_product_images');

    // Hiển thị thông báo thành công (có thể thêm sau)
    // Chuyển hướng đến trang danh sách sản phẩm (có thể thêm sau)
  };

  // Xử lý khi upload ảnh từ máy tính
  const handleImageUpload = (_file: File, dataUrl: string) => {
    // Thêm ảnh vào danh sách ngay lập tức
    const newImage: ProductImage = {
      id: `img-${Date.now()}`,
      type: 'image',
      src: dataUrl,
      isCover: productImages.length === 0, // Ảnh đầu tiên là ảnh bìa
    };
    setProductImages(prev => [...prev, newImage]);
    // Không cần lưu vào tempImage nữa vì đã thêm vào danh sách
  };

  // Xử lý khi thêm ảnh vào danh sách
  const handleAddImage = () => {
    let newImage: ProductImage | null = null;

    if (uploadType === 'image' && tempImage) {
      newImage = {
        id: `img-${Date.now()}`,
        type: 'image',
        src: tempImage,
        isCover: productImages.length === 0, // Ảnh đầu tiên là ảnh bìa
      };
      setTempImage(null);
    } else if (uploadType === 'url' && imageUrl) {
      newImage = {
        id: `url-${Date.now()}`,
        type: 'url',
        src: imageUrl,
        isCover: productImages.length === 0,
      };
      setImageUrl('');
    } else if (uploadType === 'video' && videoUrl) {
      newImage = {
        id: `video-${Date.now()}`,
        type: 'video',
        src: videoUrl,
        isCover: productImages.length === 0,
      };
      setVideoUrl('');
    }

    if (newImage) {
      setProductImages(prev => [...prev, newImage!]);
    }
  };

  // Xử lý khi xóa ảnh
  const handleRemoveImage = (id: string) => {
    setProductImages(prev => {
      const newImages = prev.filter(img => img.id !== id);

      // Nếu xóa ảnh bìa, cập nhật ảnh đầu tiên trong danh sách thành ảnh bìa
      if (prev.find(img => img.id === id)?.isCover && newImages.length > 0) {
        return newImages.map((img, index) => (index === 0 ? { ...img, isCover: true } : img));
      }

      return newImages;
    });
  };

  // Xử lý khi đặt ảnh làm ảnh bìa
  const handleSetCover = (id: string) => {
    setProductImages(prev =>
      prev.map(img => ({
        ...img,
        isCover: img.id === id,
      }))
    );
  };

  return (
    <div>
      {/* Form */}
      <Form
        schema={productSchema}
        defaultValues={defaultValues}
        onSubmit={values => handleSubmit(values as ProductFormValues)}
      >
        <Grid
          columns={{ xs: 1, sm: 1, md: 1, lg: 3, xl: 3 }}
          columnGap="lg"
          rowGap="md"
          className="mb-6"
        >
          {/* Thông tin sản phẩm - Cột 1-2 */}
          <div className="lg:col-span-2">
            <Card>
              <Typography variant="h6" className="flex items-center mb-4">
                {t('product.productInfo')}
              </Typography>
              <FormGrid columns={1} columnsMd={1} columnsSm={1} gap="md">
                <div className="w-full">
                  <label className="block text-sm font-medium mb-1">
                    {t('product.fields.name')} <span className="text-red-500">*</span>
                  </label>
                  <Controller
                    name="name"
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder={`${t('common.enter')} ${t('product.fields.name').toLowerCase()}...`}
                        className=""
                        fullWidth
                      />
                    )}
                  />
                </div>

                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    <Typography variant="body1" className="font-medium">
                      {t('product.fields.price')} <span className="text-red-500">*</span>
                    </Typography>
                  </div>
                  <div className="ml-4">
                    <Controller
                      name="priceType"
                      render={({ field }) => (
                        <RadioGroup
                          {...field}
                          options={[
                            { label: t('product.fields.priceTypes.yes'), value: 'có' },
                            { label: t('product.fields.priceTypes.no'), value: 'không' },
                            { label: t('product.fields.priceTypes.other'), value: 'khác' },
                          ]}
                          direction="horizontal"
                          className="flex items-center space-x-2"
                          size="md"
                          color="primary"
                          variant="default"
                        />
                      )}
                    />
                  </div>
                </div>

                {/* Hiển thị các trường giá tùy theo lựa chọn */}
                <Controller
                  name="priceType"
                  render={({ field }) => <ConditionalPriceFields priceType={field.value} />}
                />

                <Grid columns={{ xs: 1, sm: 1 }} columnGap="md">
                  <Controller name="brand" render={({ field }) => <TagsInput field={field} />} />
                </Grid>
              </FormGrid>
            </Card>

            {/* Thuộc tính sản phẩm */}
            <Card className="mb-6 mt-10 shadow-sm hover:shadow-md transition-shadow duration-300">
              <Typography variant="h6" className="flex items-center mb-4">
                {t('product.productAttributes')}
              </Typography>
              <FormArray
                name="attributes"
                addButtonText={t('common.add') + ' ' + t('product.fields.attributes').toLowerCase()}
                renderItem={index => (
                  <AttributeItemRenderer index={index} attributeTypes={attributeTypes} />
                )}
              />
            </Card>
          </div>

          {/* Ảnh sản phẩm - Cột 3 */}
          <div>
            <Card className="mb-6 shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="mb-4">
                <div className="border-b border-gray-100 dark:border-gray-800 mb-4 pb-4">
                  <Typography variant="h6" className="flex items-center">
                    <Icon name="image" size="md" className="mr-2 text-primary" />
                    {t('product.images.addImages')}
                  </Typography>
                </div>

                <div className="grid grid-cols-3 gap-0 border-b border-gray-200 dark:border-gray-700 mb-6">
                  <button
                    className={`py-3 font-medium text-sm transition-colors flex items-center justify-center ${
                      uploadType === 'image'
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                    onClick={() => setUploadType('image')}
                  >
                    <Icon name="image" size="sm" className="mr-2" />
                    {t('product.images.image')}
                  </button>
                  <button
                    className={`py-3 font-medium text-sm transition-colors flex items-center justify-center ${
                      uploadType === 'url'
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                    onClick={() => setUploadType('url')}
                  >
                    <Icon name="link" size="sm" className="mr-2" />
                    {t('product.images.url')}
                  </button>
                  <button
                    className={`py-3 font-medium text-sm transition-colors flex items-center justify-center ${
                      uploadType === 'video'
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                    onClick={() => setUploadType('video')}
                  >
                    <Icon name="link" size="sm" className="mr-2" />
                    {t('product.images.video')}
                  </button>
                </div>

                <div className="p-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg">
                  {uploadType === 'image' && (
                    <div>
                      <ImageUploader
                        currentImage={null}
                        onImageUpload={handleImageUpload}
                        height="h-64"
                        placeholder={t('product.images.dragAndDrop')}
                        className="mb-2 border-dashed border-2 border-gray-300 hover:border-primary transition-colors"
                      />
                      <Typography
                        variant="caption"
                        className="text-gray-500 dark:text-gray-400 mt-2 block"
                      >
                        {t('product.images.recommendedSize')}
                      </Typography>
                    </div>
                  )}

                  {uploadType === 'url' && (
                    <div>
                      <div className="flex items-center gap-2">
                        <div className="flex-grow">
                          <div className="w-full">
                            <label className="block text-sm font-medium mb-1">
                              {t('product.images.enterImageUrl')}
                            </label>
                            <div className="flex items-center">
                              <Input
                                placeholder={`${t('common.enter')} URL...`}
                                value={imageUrl}
                                onChange={e => setImageUrl(e.target.value)}
                                className=""
                                fullWidth
                              />
                              <Button
                                onClick={handleAddImage}
                                disabled={!imageUrl}
                                variant="primary"
                                size="sm"
                                className="ml-2 w-10 h-10 p-0 rounded-full shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                                title={t('product.images.addToList')}
                              >
                                <Icon name="plus" size="md" className="text-white" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {uploadType === 'video' && (
                    <div>
                      <div className="flex items-center gap-2">
                        <div className="flex-grow">
                          <div className="w-full">
                            <label className="block text-sm font-medium mb-1">
                              {t('product.images.enterVideoUrl')}
                            </label>
                            <div className="flex items-center">
                              <Input
                                placeholder={`${t('common.enter')} ${t('product.images.video')} URL...`}
                                value={videoUrl}
                                onChange={e => setVideoUrl(e.target.value)}
                                className=""
                                fullWidth
                              />
                              <Button
                                onClick={handleAddImage}
                                disabled={!videoUrl}
                                variant="primary"
                                size="sm"
                                className="ml-2 w-10 h-10 p-0 rounded-full shadow-md hover:shadow-lg hover:scale-105 transition-all duration-300"
                                title={t('product.images.addToList')}
                              >
                                <Icon name="plus" size="md" className="text-white" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Danh sách tất cả hình ảnh và video */}
                {productImages.length > 0 && (
                  <div className="mt-6 space-y-4">
                    {/* Danh sách ảnh tải lên */}
                    {productImages.filter(img => img.type === 'image').length > 0 && (
                      <Accordion
                        items={[
                          {
                            key: 'product-images',
                            title: (
                              <div className="flex items-center">
                                <Typography variant="subtitle1" className="font-medium">
                                  {t('product.images.uploadedImages')}
                                </Typography>
                                <span className="ml-2 px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">
                                  {productImages.filter(img => img.type === 'image').length}
                                </span>
                              </div>
                            ),
                            icon: <Icon name="image" size="md" className="text-primary" />,
                            content: (
                              <ScrollArea className="max-h-96">
                                <div className="space-y-3">
                                  {productImages
                                    .filter(img => img.type === 'image')
                                    .map(image => (
                                      <div
                                        key={image.id}
                                        className={`flex items-center p-3 rounded-lg border ${
                                          image.isCover
                                            ? 'border-primary bg-primary/5'
                                            : 'border-gray-200 dark:border-gray-700'
                                        }`}
                                      >
                                        <div className="w-16 h-16 mr-3 rounded-md overflow-hidden flex-shrink-0 bg-gray-100 dark:bg-gray-800">
                                          <img
                                            src={image.src}
                                            alt=""
                                            className="w-full h-full object-cover"
                                            onError={e => {
                                              e.currentTarget.src =
                                                'https://via.placeholder.com/64?text=Error';
                                            }}
                                          />
                                        </div>
                                        <div className="flex-grow min-w-0">
                                          <div className="flex items-center">
                                            <Typography
                                              variant="body2"
                                              className="font-medium truncate"
                                            >
                                              {t('product.images.image')}
                                            </Typography>
                                            {image.isCover && (
                                              <span className="ml-2 px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">
                                                {t('product.images.coverImage')}
                                              </span>
                                            )}
                                          </div>
                                          <Typography
                                            variant="caption"
                                            className="text-gray-500 truncate block"
                                          >
                                            {t('product.images.uploadedFromComputer')}
                                          </Typography>
                                        </div>
                                        <div className="flex items-center ml-2 space-x-1">
                                          {!image.isCover && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleSetCover(image.id)}
                                              title={t('product.images.setCover')}
                                            >
                                              <Icon name="star" size="sm" />
                                            </Button>
                                          )}
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleRemoveImage(image.id)}
                                            title={t('common.delete')}
                                          >
                                            <Icon name="close" size="sm" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </ScrollArea>
                            ),
                          },
                        ]}
                        type="bordered"
                        defaultActiveKeys={['product-images']}
                        className="border-gray-200 dark:border-gray-700"
                      />
                    )}

                    {/* Danh sách ảnh từ URL */}
                    {productImages.filter(img => img.type === 'url').length > 0 && (
                      <Accordion
                        items={[
                          {
                            key: 'url-images',
                            title: (
                              <div className="flex items-center">
                                <Typography variant="subtitle1" className="font-medium">
                                  {t('product.images.urlImages')}
                                </Typography>
                                <span className="ml-2 px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">
                                  {productImages.filter(img => img.type === 'url').length}
                                </span>
                              </div>
                            ),
                            icon: <Icon name="link" size="md" className="text-blue-500" />,
                            content: (
                              <ScrollArea className="max-h-96">
                                <div className="space-y-3">
                                  {productImages
                                    .filter(img => img.type === 'url')
                                    .map(image => (
                                      <div
                                        key={image.id}
                                        className={`flex items-center p-3 rounded-lg border ${
                                          image.isCover
                                            ? 'border-primary bg-primary/5'
                                            : 'border-gray-200 dark:border-gray-700'
                                        }`}
                                      >
                                        <div className="w-16 h-16 mr-3 rounded-md overflow-hidden flex-shrink-0 bg-gray-100 dark:bg-gray-800">
                                          <img
                                            src={image.src}
                                            alt=""
                                            className="w-full h-full object-cover"
                                            onError={e => {
                                              e.currentTarget.src =
                                                'https://via.placeholder.com/64?text=Error';
                                            }}
                                          />
                                        </div>
                                        <div className="flex-grow min-w-0">
                                          <div className="flex items-center">
                                            <Typography
                                              variant="body2"
                                              className="font-medium truncate"
                                            >
                                              URL
                                            </Typography>
                                          </div>
                                          <Typography
                                            variant="caption"
                                            className="text-gray-500 truncate block"
                                          >
                                            {image.src}
                                          </Typography>
                                        </div>
                                        <div className="flex items-center ml-2 space-x-1">
                                          {!image.isCover && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleSetCover(image.id)}
                                              title={t('product.images.setCover')}
                                            >
                                              <Icon name="star" size="sm" />
                                            </Button>
                                          )}
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleRemoveImage(image.id)}
                                            title={t('common.delete')}
                                          >
                                            <Icon name="close" size="sm" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </ScrollArea>
                            ),
                          },
                        ]}
                        type="bordered"
                        defaultActiveKeys={['url-images']}
                        className="border-gray-200 dark:border-gray-700"
                      />
                    )}

                    {/* Danh sách video */}
                    {productImages.filter(img => img.type === 'video').length > 0 && (
                      <Accordion
                        items={[
                          {
                            key: 'video-list',
                            title: (
                              <div className="flex items-center">
                                <Typography variant="subtitle1" className="font-medium">
                                  {t('product.images.videoList')}
                                </Typography>
                                <span className="ml-2 px-2 py-0.5 text-xs bg-primary/10 text-primary rounded-full">
                                  {productImages.filter(img => img.type === 'video').length}
                                </span>
                              </div>
                            ),
                            icon: (
                              <div className="relative w-5 h-5 flex items-center justify-center">
                                <div className="absolute inset-0 w-5 h-4 bg-red-500 rounded-sm flex items-center justify-center">
                                  <Icon name="chevron-right" size="xs" className="text-white" />
                                </div>
                              </div>
                            ),
                            content: (
                              <ScrollArea className="max-h-96">
                                <div className="space-y-3">
                                  {productImages
                                    .filter(img => img.type === 'video')
                                    .map(image => (
                                      <div
                                        key={image.id}
                                        className={`flex items-center p-3 rounded-lg border ${
                                          image.isCover
                                            ? 'border-primary bg-primary/5'
                                            : 'border-gray-200 dark:border-gray-700'
                                        }`}
                                      >
                                        <div className="w-16 h-16 mr-3 rounded-md overflow-hidden flex-shrink-0 bg-gray-100 dark:bg-gray-800">
                                          <div className="w-full h-full flex flex-col items-center justify-center bg-gray-200 dark:bg-gray-700">
                                            <div className="relative w-8 h-6 bg-gray-500 rounded-sm overflow-hidden flex items-center justify-center">
                                              <div className="absolute inset-0 flex items-center justify-center">
                                                <Icon
                                                  name="chevron-right"
                                                  size="xs"
                                                  className="text-white"
                                                />
                                              </div>
                                            </div>
                                            <span className="text-xs mt-1 text-gray-500">
                                              {t('product.images.video')}
                                            </span>
                                          </div>
                                        </div>
                                        <div className="flex-grow min-w-0">
                                          <div className="flex items-center">
                                            <Typography
                                              variant="body2"
                                              className="font-medium truncate"
                                            >
                                              {t('product.images.video')} URL
                                            </Typography>
                                          </div>
                                          <Typography
                                            variant="caption"
                                            className="text-gray-500 truncate block"
                                          >
                                            {image.src}
                                          </Typography>
                                        </div>
                                        <div className="flex items-center ml-2 space-x-1">
                                          {!image.isCover && (
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleSetCover(image.id)}
                                              title={t('product.images.setCover')}
                                            >
                                              <Icon name="star" size="sm" />
                                            </Button>
                                          )}
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleRemoveImage(image.id)}
                                            title={t('common.delete')}
                                          >
                                            <Icon name="close" size="sm" />
                                          </Button>
                                        </div>
                                      </div>
                                    ))}
                                </div>
                              </ScrollArea>
                            ),
                          },
                        ]}
                        type="bordered"
                        defaultActiveKeys={['video-list']}
                        className="border-gray-200 dark:border-gray-700"
                      />
                    )}
                  </div>
                )}
              </div>
            </Card>

            {/* Nút submit */}
            <div className="flex flex-col sm:flex-row justify-end gap-4">
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="w-full sm:w-auto"
              >
                <Icon name="chevron-left" size="sm" className="mr-1" />
                {t('product.actions.cancelCreation')}
              </Button>
              <Button type="submit" className="w-full sm:w-auto">
                <Icon name="check" size="sm" className="mr-1" />
                {t('product.actions.createProduct')}
              </Button>
            </div>
          </div>
        </Grid>
      </Form>
    </div>
  );
};

/**
 * Component hiển thị các trường giá tùy theo lựa chọn
 */
interface ConditionalPriceFieldsProps {
  priceType: string;
}

const ConditionalPriceFields: React.FC<ConditionalPriceFieldsProps> = ({ priceType }) => {
  // Sử dụng useFormContext từ react-hook-form
  const formContext = useFormContext();
  const { t } = useTranslation('business');

  if (priceType === 'có') {
    return (
      <FormGrid columns={2} gap="md">
        <div className="w-full">
          <label className="block text-sm font-medium mb-1">
            {t('product.fields.regularPrice')}
          </label>
          <div className="relative">
            <Controller
              control={formContext.control}
              name="regularPrice"
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder={`${t('common.enter')} ${t('product.fields.regularPrice').toLowerCase()}...`}
                  className="pr-12"
                  fullWidth
                />
              )}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              VND
            </span>
          </div>
        </div>
        <div className="w-full">
          <label className="block text-sm font-medium mb-1">{t('product.fields.salePrice')}</label>
          <div className="relative">
            <Controller
              control={formContext.control}
              name="salePrice"
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder={`${t('common.enter')} ${t('product.fields.salePrice').toLowerCase()}...`}
                  className="pr-12"
                  fullWidth
                />
              )}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              VND
            </span>
          </div>
        </div>
      </FormGrid>
    );
  }

  if (priceType === 'khác') {
    return (
      <div className="w-full">
        <label className="block text-sm font-medium mb-1">{t('product.fields.priceNote')}</label>
        <Controller
          control={formContext.control}
          name="priceNote"
          render={({ field }) => (
            <Input
              {...field}
              placeholder={`${t('common.enter')} ${t('product.fields.priceNote').toLowerCase()}...`}
              fullWidth
            />
          )}
        />
      </div>
    );
  }

  return null;
};

/**
 * Component nhập tags
 */
interface TagsInputProps {
  // Sử dụng kiểu dữ liệu chung cho field
  field: {
    value: string;
    onChange: (value: string) => void;
  };
}

// Định nghĩa kiểu cho variant của Chip
type ChipVariant = 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default';

const TagsInput: React.FC<TagsInputProps> = ({ field }) => {
  const { t } = useTranslation('business');
  const [inputValue, setInputValue] = useState('');
  const [tags, setTags] = useState<Array<{ text: string; variant: ChipVariant }>>(
    field.value
      ? field.value
          .split(',')
          .filter(Boolean)
          .map((tag: string) => ({
            text: tag,
            variant: getRandomChipVariant(),
          }))
      : []
  );

  // Hàm tạo variant ngẫu nhiên cho chip
  function getRandomChipVariant(): ChipVariant {
    const variants: ChipVariant[] = ['primary', 'success', 'warning', 'danger', 'info'];
    const randomIndex = Math.floor(Math.random() * variants.length);
    return variants[randomIndex] || 'primary'; // Fallback to 'primary' if undefined
  }

  // Xử lý khi thêm tag mới
  const handleAddTag = (tag: string) => {
    if (tag.trim() && !tags.some(t => t.text === tag.trim())) {
      const newTag = {
        text: tag.trim(),
        variant: getRandomChipVariant(),
      };
      const newTags = [...tags, newTag];
      setTags(newTags);
      field.onChange(newTags.map(t => t.text).join(','));
      setInputValue('');
    }
  };

  // Xử lý khi xóa tag
  const handleDeleteTag = (tagToDelete: string) => {
    const newTags = tags.filter(tag => tag.text !== tagToDelete);
    setTags(newTags);
    field.onChange(newTags.map(t => t.text).join(','));
  };

  // Xử lý khi nhấn phím
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      handleAddTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      // Xóa tag cuối cùng khi nhấn Backspace và input rỗng
      const newTags = tags.slice(0, -1);
      setTags(newTags);
      field.onChange(newTags.map(t => t.text).join(','));
    }
  };

  return (
    <div className="w-full">
      <label className="block text-sm font-medium mb-1">{t('product.fields.brand')}</label>
      <div className="border border-gray-300 dark:border-gray-700 rounded-md p-1 flex flex-wrap gap-2 focus-within:outline-none transition-all duration-200">
        {/* Hiển thị các tag đã thêm */}
        {tags.map((tag, index) => (
          <Chip
            key={index}
            variant={tag.variant}
            size="sm"
            closable
            onClose={() => handleDeleteTag(tag.text)}
          >
            {tag.text}
          </Chip>
        ))}

        {/* Input để nhập tag mới */}
        <input
          type="text"
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={() => inputValue.trim() && handleAddTag(inputValue)}
          placeholder={
            tags.length === 0
              ? `${t('common.enter')} ${t('product.fields.brand').toLowerCase()} ${t('common.and')} ${t('common.pressEnter')}...`
              : ''
          }
          className="flex-grow min-w-[120px] outline-none border-none bg-transparent dark:text-white py-1 px-2"
        />
      </div>
    </div>
  );
};

/**
 * Component hiển thị một thuộc tính sản phẩm
 */
interface AttributeItemRendererProps {
  index: number;
  attributeTypes: Array<{ label: string; value: string }>;
}

const AttributeItemRenderer: React.FC<AttributeItemRendererProps> = ({ index, attributeTypes }) => {
  // Sử dụng useFormContext ở cấp cao nhất của component
  const formContext = useFormContext();
  const { t } = useTranslation('business');

  return (
    <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800/30 rounded-lg border border-gray-200 dark:border-gray-700">
      <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 3 }} columnGap="md" rowGap="md">
        <div className="w-full">
          <label className="block text-sm font-medium mb-1">
            {t('product.fields.attributeName')} <span className="text-red-500">*</span>
          </label>
          <Controller
            control={formContext.control}
            name={`attributes.${index}.name`}
            render={({ field }) => (
              <Input
                {...field}
                placeholder={`${t('common.enter')} ${t('product.fields.attributeName').toLowerCase()}...`}
                fullWidth
                className="border border-gray-300 dark:border-gray-700 rounded-md"
              />
            )}
          />
        </div>

        <div className="w-full">
          <label className="block text-sm font-medium mb-1">
            {t('product.fields.attributeType')} <span className="text-red-500">*</span>
          </label>
          <Controller
            control={formContext.control}
            name={`attributes.${index}.type`}
            render={({ field }) => (
              <Select
                {...field}
                options={attributeTypes}
                placeholder={`${t('common.select')} ${t('product.fields.attributeType').toLowerCase()}...`}
                fullWidth
                className="border border-gray-300 dark:border-gray-700 rounded-md"
              />
            )}
          />
        </div>

        <div className="flex items-end">
          <div className="flex-grow">
            <label className="block text-sm font-medium mb-1">
              {t('product.fields.attributeValue')}
            </label>
            <Controller
              control={formContext.control}
              name={`attributes.${index}.value`}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder={`${t('common.enter')} ${t('product.fields.attributeValue').toLowerCase()}...`}
                  fullWidth
                  className="border border-gray-300 dark:border-gray-700 rounded-md"
                />
              )}
            />
          </div>
        </div>
      </Grid>
    </div>
  );
};

export default CreateProductPage;
