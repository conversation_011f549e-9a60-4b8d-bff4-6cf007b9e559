import React from 'react';

export interface StatusBadgeProps {
  // Văn bản hiển thị
  text: string;
  // Biến thể màu sắc ('primary', 'success', 'danger', 'warning', 'info')
  variant?: 'primary' | 'success' | 'danger' | 'warning' | 'info';
  // Class bổ sung (tùy chọn)
  className?: string;
}

/**
 * Component StatusBadge - hiển thị badge trạng thái đơn giản
 * Hiển thị văn bản với màu sắc tùy theo biến thể
 */
const StatusBadge: React.FC<StatusBadgeProps> = ({
  text,
  variant = 'primary',
  className = '',
}) => {
  const variantClasses: Record<string, string> = {
    primary: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    danger: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    info: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  };

  const colorClass = variantClasses[variant] || variantClasses['primary'];

  return (
    <div
      className={`px-2 py-1 rounded-full text-center text-xs font-medium ${colorClass} ${className}`}
    >
      {text}
    </div>
  );
};

export default StatusBadge;
