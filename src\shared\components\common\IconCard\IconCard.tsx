import React from 'react';
import Icon from '@/shared/components/common/Icon';
import type { IconName } from '@/shared/components/common/Icon/Icon';
import { useTheme } from '@/shared/contexts/theme';
import { Tooltip } from '@/shared/components/common';

export interface IconCardProps {
  /**
   * Tên icon
   */
  icon: IconName;

  /**
   * Hàm xử lý khi click
   */
  onClick?: (() => void) | undefined;

  /**
   * <PERSON><PERSON>ch thước của icon card
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant của icon card
   */
  variant?: 'default' | 'primary' | 'secondary' | 'ghost' | 'danger';

  /**
   * Tooltip hiển thị khi hover
   */
  title?: string;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Trạng thái active
   */
  active?: boolean;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Component IconCard hiển thị icon trong card bo tròn với phong cách hiện đại
 */
const IconCard: React.FC<IconCardProps> = ({
  icon,
  onClick,
  size = 'md',
  variant = 'default',
  title,
  disabled = false,
  className = '',
  active = false,
  isLoading = false,
}) => {
  const { themeMode } = useTheme();
  const isDark = themeMode === 'dark';

  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  }[size];

  // Xác định màu sắc dựa trên variant
  const variantClasses = {
    default: `bg-white dark:bg-dark-light text-gray-700 dark:text-gray-300
              hover:bg-gray-100 dark:hover:bg-dark-lighter`,
    primary: `bg-white dark:bg-dark-light text-primary dark:text-primary-dark
              hover:bg-primary-50 dark:hover:bg-primary-900/20`,
    secondary: `bg-white dark:bg-dark-light text-gray-700 dark:text-gray-300
                hover:bg-gray-100 dark:hover:bg-dark-lighter`,
    ghost: `bg-transparent text-gray-700 dark:text-gray-300
            hover:bg-gray-100 dark:hover:bg-dark-lighter`,
            danger: `bg-white dark:bg-dark-light text-red-500 dark:text-red-400
            hover:bg-red-50 dark:hover:bg-red-900/20`,
  }[variant];

  // Xác định màu icon
  const iconColor =
    variant === 'primary'
      ? isDark
        ? '#FF6666'
        : '#FF3333' // Màu thương hiệu
      : undefined;

  // Xác định class cho trạng thái active
  const activeClasses = active ? 'ring-2 ring-primary/30 dark:ring-primary-dark/30' : '';

  // Xác định class cho trạng thái disabled
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : '';

  // Kích thước icon dựa trên kích thước card
  const iconSize = {
    sm: 'sm',
    md: 'md',
    lg: 'lg',
  }[size] as 'sm' | 'md' | 'lg';

  const iconCardElement = (
    <div
      className={`
        ${sizeClasses}
        ${variantClasses}
        ${activeClasses}
        ${disabledClasses}
        ${className}
        flex items-center justify-center rounded-full shadow-sm
        transition-colors duration-200 ease-in-out
        ${!disabled && onClick ? 'cursor-pointer' : ''}
      `}
      onClick={disabled ? undefined : onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick && !disabled ? 0 : undefined}
      style={{ WebkitTapHighlightColor: 'transparent' }}
    >
      {isLoading ? (
        <Icon name="loading" size={iconSize} className="animate-spin" />
      ) : (
        <Icon name={icon} size={iconSize} color={iconColor} />
      )}
    </div>
  );

  // Nếu có title, wrap với Tooltip
  if (title) {
    return (
      <Tooltip content={title} position="bottom">
        {iconCardElement}
      </Tooltip>
    );
  }

  return iconCardElement;
};

export default IconCard;
