import React from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, Card, Typography } from '@/shared/components/common';
import { useProduct } from '../../hooks/useProductQuery';
import { ProductTypeEnum } from '../../types/product.types';
import PhysicalProductEditForm from './PhysicalProductEditForm';
import DigitalProductEditForm from './DigitalProductEditForm';
import ServiceProductEditForm from './ServiceProductEditForm';
import EventProductEditForm from './EventProductEditForm';
import ComboProductEditForm from './ComboProductEditForm';

// Note: Response interfaces are now handled by individual form components

interface ProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
}

/**
 * Component để render form edit phù hợp với loại sản phẩm
 */
const ProductEditForm: React.FC<ProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm
  const { data: product, isLoading: isLoadingProduct, error: productError } = useProduct(productId);

  // Hiển thị loading khi đang fetch chi tiết sản phẩm
  if (isLoadingProduct) {
    return (
      <Card title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Loading size="lg" />
          <Typography variant="body1" className="ml-3">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </Card>
    );
  }

  // Hiển thị lỗi nếu không tải được sản phẩm
  if (productError || !product) {
    return (
      <Card title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </Card>
    );
  }

  // Render form phù hợp với loại sản phẩm
  const renderProductEditForm = () => {
    switch (product.productType) {
      case ProductTypeEnum.SERVICE:
        return (
          <ServiceProductEditForm
            productId={productId}
            onCancel={onCancel}
            {...(onSuccess ? { onSuccess } : {})}
          />
        );

      case ProductTypeEnum.EVENT:
        return (
          <EventProductEditForm
            productId={productId}
            product={product}
            onCancel={onCancel}
          />
        );

      case ProductTypeEnum.COMBO:
        return (
          <ComboProductEditForm
            productId={productId}
            onCancel={onCancel}
            {...(onSuccess ? { onSuccess } : {})}
          />
        );

      case ProductTypeEnum.DIGITAL:
        return (
          <DigitalProductEditForm
            productId={productId}
            onCancel={onCancel}
            {...(onSuccess ? { onSuccess } : {})}
          />
        );

      case ProductTypeEnum.PHYSICAL:
      default:
        return (
          <PhysicalProductEditForm
            productId={productId}
            onCancel={onCancel}
            {...(onSuccess ? { onSuccess } : {})}
          />
        );
    }
  };

  return renderProductEditForm();
};

export default ProductEditForm;
