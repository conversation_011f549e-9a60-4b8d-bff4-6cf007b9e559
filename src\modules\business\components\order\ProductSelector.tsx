import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Input,
  Button,
  Icon,
  Table,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { ProductService } from '../../services/product.service';
import { OrderItemDto } from '../../types/order.types';
import { ProductDto, HasPriceDto } from '../../types/product.types';
import { formatCurrency } from '@/shared/utils/format';

interface ProductSelectorProps {
  selectedItems: OrderItemDto[];
  onItemsChange: (items: OrderItemDto[]) => void;
}

/**
 * Component chọn sản phẩm cho đơn hàng
 */
const ProductSelector: React.FC<ProductSelectorProps> = ({
  selectedItems,
  onItemsChange,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Store loaded products for later use
  const [loadedProducts, setLoadedProducts] = useState<Map<string, ProductDto>>(new Map());

  // Helper function to safely extract price from product
  const getProductPrice = useCallback((product: ProductDto): number => {
    if (!product.price || typeof product.price !== 'object') {
      return 0;
    }

    const priceObj = product.price as HasPriceDto;
    return priceObj.salePrice || priceObj.listPrice || 0;
  }, []);

  // Load products function for AsyncSelectWithPagination
  const loadProducts = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await ProductService.getProducts({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
      });

      // Store products in state for later retrieval
      const newProducts = new Map(loadedProducts);
      response.items.forEach((product: ProductDto) => {
        newProducts.set(product.id.toString(), product);
      });
      setLoadedProducts(newProducts);

      return {
        items: response.items.map((product: ProductDto) => ({
          value: product.id.toString(),
          label: product.name || 'N/A',
          subtitle: formatCurrency(getProductPrice(product)),
          data: product as unknown as Record<string, unknown>,
        })),
        totalItems: response.meta.totalItems,
        totalPages: response.meta.totalPages,
        currentPage: response.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading products:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, [loadedProducts, getProductPrice]);

  // Xử lý thêm sản phẩm vào đơn hàng
  const handleAddProduct = useCallback((product: ProductDto) => {
    const existingItemIndex = selectedItems.findIndex(
      item => item.productId === product.id
    );

    if (existingItemIndex >= 0) {
      // Nếu sản phẩm đã có, tăng số lượng
      const updatedItems = [...selectedItems];
      const existingItem = updatedItems[existingItemIndex];
      if (existingItem) {
        existingItem.quantity += 1;
        existingItem.totalPrice = existingItem.quantity * existingItem.price;
        onItemsChange(updatedItems);
      }
    } else {
      // Thêm sản phẩm mới
      const price = getProductPrice(product);

      // Create the new item with proper type handling
      const newItem: OrderItemDto = {
        productId: product.id,
        productName: product.name || 'N/A',
        quantity: 1,
        price: price,
        totalPrice: price,
        ...(product.productType && { productType: product.productType }),
        ...(product.shipmentConfig && { shipmentConfig: product.shipmentConfig }),
        ...(product.images?.[0]?.url && { image: product.images[0].url }),
      };
      onItemsChange([...selectedItems, newItem]);
      console.log('Added product:', newItem);
    }
  }, [selectedItems, onItemsChange, getProductPrice]);

  // Handle product selection from AsyncSelectWithPagination
  const handleProductSelect = useCallback((value: string | number | string[] | number[] | undefined) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return;
    }

    // Get the first value if it's an array
    const productId = Array.isArray(value) ? value[0] : value;

    // Check if productId is valid before converting to string
    if (productId === undefined || productId === null) {
      return;
    }

    const productIdStr = productId.toString();

    // Get product data from loaded products
    const productData = loadedProducts.get(productIdStr);

    if (productData) {
      handleAddProduct(productData);
    }
  }, [loadedProducts, handleAddProduct]);

  // Xử lý xóa sản phẩm
  const handleRemoveItem = useCallback((index: number) => {
    const updatedItems = selectedItems.filter((_, i) => i !== index);
    onItemsChange(updatedItems);
  }, [selectedItems, onItemsChange]);

  // Xử lý cập nhật số lượng sản phẩm
  const handleUpdateQuantity = useCallback((index: number, quantity: number) => {
    if (quantity <= 0) {
      handleRemoveItem(index);
      return;
    }

    const updatedItems = [...selectedItems];
    const item = updatedItems[index];
    if (item) {
      item.quantity = quantity;
      item.totalPrice = quantity * item.price;
      onItemsChange(updatedItems);
    }
  }, [selectedItems, onItemsChange, handleRemoveItem]);

  // Xử lý cập nhật giá sản phẩm
  const handleUpdatePrice = useCallback((index: number, price: number) => {
    const updatedItems = [...selectedItems];
    const item = updatedItems[index];
    if (item) {
      item.price = price;
      item.totalPrice = item.quantity * price;
      onItemsChange(updatedItems);
    }
  }, [selectedItems, onItemsChange]);

  // Tính tổng tiền
  const subtotal = useMemo(() => {
    return selectedItems.reduce((sum, item) => sum + item.totalPrice, 0);
  }, [selectedItems]);

  // Cấu hình columns cho bảng sản phẩm đã chọn
  const selectedItemsColumns = [
    {
      title: t('business:product.name'),
      dataIndex: 'productName',
      key: 'productName',
      render: (value: unknown, record: OrderItemDto) => {
        const productName = value as string;
        return (
          <div className="flex items-center gap-2">
            {record.image && (
              <img
                src={record.image}
                alt={productName}
                className="w-10 h-10 object-cover rounded"
              />
            )}
            <div>
              <Typography variant="subtitle2">{productName}</Typography>
              {record.variantName && (
                <Typography variant="caption" className="text-gray-500">
                  {record.variantName}
                </Typography>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: t('business:order.quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
      width: 120,
      render: (value: unknown, _: OrderItemDto, index: number) => {
        const quantity = value as number;
        return (
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleUpdateQuantity(index, quantity - 1)}
              disabled={quantity <= 1}
            >
              <Icon name="minus" size="sm" />
            </Button>
            <Input
              type="number"
              value={quantity}
              onChange={(e) => handleUpdateQuantity(index, parseInt(e.target.value) || 1)}
              className="w-16 text-center"
              min={1}
            />
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleUpdateQuantity(index, quantity + 1)}
            >
              <Icon name="plus" size="sm" />
            </Button>
          </div>
        );
      },
    },
    {
      title: t('business:product.price'),
      dataIndex: 'price',
      key: 'price',
      width: 120,
      render: (value: unknown, _: OrderItemDto, index: number) => {
        const price = value as number;
        return (
          <Input
            type="number"
            value={price}
            onChange={(e) => handleUpdatePrice(index, parseFloat(e.target.value) || 0)}
            className="w-24"
            min={0}
          />
        );
      },
    },
    {
      title: t('business:order.totalPrice'),
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: 120,
      render: (value: unknown) => {
        const totalPrice = value as number;
        return (
          <Typography variant="subtitle2" className="font-medium">
            {formatCurrency(totalPrice)}
          </Typography>
        );
      },
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 80,
      render: (_: unknown, __: OrderItemDto, index: number) => (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => handleRemoveItem(index)}
          className="text-red-500 hover:text-red-700"
        >
          <Icon name="trash" size="sm" />
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Typography variant="h6" className="mb-4">
        {t('business:order.selectProducts')}
      </Typography>

      {/* AsyncSelectWithPagination cho chọn sản phẩm */}
      <div className="mb-4">
        <AsyncSelectWithPagination
          loadOptions={loadProducts}
          placeholder={t('business:order.searchProductPlaceholder', 'Tìm kiếm sản phẩm...')}
          searchOnEnter={true}
          debounceTime={300}
          itemsPerPage={20}
          noOptionsMessage={t('business:order.noProductsFound', 'Không tìm thấy sản phẩm')}
          loadingMessage={t('common:loading', 'Đang tải...')}
          onChange={handleProductSelect}
          fullWidth
        />
      </div>

      {/* Bảng sản phẩm đã chọn */}
      {selectedItems.length > 0 && (
        <Card>
          <div className="p-4">
            <Typography variant="subtitle1" className="mb-4">
              {t('business:order.selectedProducts')} ({selectedItems.length})
            </Typography>

            <Table
              columns={selectedItemsColumns}
              data={selectedItems}
              pagination={false}
            />

            <div className="flex justify-end mt-4 pt-4">
              <div className="text-right">
                <Typography variant="subtitle1" className="font-semibold">
                  {t('business:order.subtotal')}: {formatCurrency(subtotal)}
                </Typography>
              </div>
            </div>
          </div>
        </Card>
      )}

      {selectedItems.length === 0 && (
        <Card>
          <div className="p-8 text-center">
            <Icon name="package" size="lg" className="mx-auto mb-4 text-gray-400" />
            <Typography variant="body1" className="text-gray-500">
              {t('business:order.noProductsSelected')}
            </Typography>
            <Typography variant="body2" className="text-gray-400 mt-1">
              {t('business:order.searchToAddProducts')}
            </Typography>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ProductSelector;
