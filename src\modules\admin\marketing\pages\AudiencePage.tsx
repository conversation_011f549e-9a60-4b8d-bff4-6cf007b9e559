import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, Chip } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import AudienceForm from '../components/forms/AudienceForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  Audience,
  AudienceFilterParams,
  AudienceFormData
} from '../types/audience.types';
import { 
  useAudiences, 
  useCreateAudience, 
  useUpdateAudience, 
  useAudience, 
  useDeleteMultipleAudiences 
} from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

/**
 * Trang quản lý audience cho admin sử dụng các hooks tối ưu
 */
const AudiencePage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho form sửa audience
  const [editingAudienceId, setEditingAudienceId] = useState<string | null>(null);

  // Sử dụng hooks từ API
  const createAudienceMutation = useCreateAudience();
  const updateAudienceMutation = useUpdateAudience();
  const { mutateAsync: deleteMultipleAudiences } = useDeleteMultipleAudiences();

  // Hooks cho form sửa audience - sử dụng conditional logic
  const { data: editingAudience } = useAudience(editingAudienceId || '');

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((id: string) => {
    setEditingAudienceId(id);
    showEditForm();
  }, [showEditForm]);

  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:audience.selectToDelete', 'Vui lòng chọn audience để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Gọi API xóa nhiều audiences cùng lúc
      await deleteMultipleAudiences(selectedRowKeys as string[]);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:audience.bulkDeleteSuccess', 'Xóa {{count}} audience thành công', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('marketing:audience.bulkDeleteError', 'Xóa nhiều audience thất bại'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleAudiences, t]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<Audience>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '10%', sortable: true },
      { key: 'name', title: 'Tên audience', dataIndex: 'name', width: '20%', sortable: true },
      { key: 'email', title: 'Email', dataIndex: 'email', width: '20%', sortable: true },
      { key: 'phone', title: 'Số điện thoại', dataIndex: 'phone', width: '15%', sortable: true },
      { key: 'description', title: 'Mô tả', dataIndex: 'description', width: '20%', sortable: true },
      {
        key: 'tagIds',
        title: 'Tags',
        dataIndex: 'tagIds',
        width: '15%',
        sortable: false,
        render: (value: unknown) => {
          const tagIds = value as number[] | undefined;
          if (!tagIds || tagIds.length === 0) {
            return <span className="text-gray-400">-</span>;
          }

          return (
            <div className="flex flex-wrap gap-1">
              {tagIds.slice(0, 2).map((tagId, index) => (
                <Chip key={index} variant="secondary" size="sm">
                  Tag {tagId}
                </Chip>
              ))}
              {tagIds.length > 2 && (
                <Chip variant="secondary" size="sm">
                  +{tagIds.length - 2}
                </Chip>
              )}
            </div>
          );
        },
      },
      { key: 'totalContacts', title: 'Số liên hệ', dataIndex: 'totalContacts', width: '10%', sortable: true },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_: unknown, record: Audience) => {
          const actionItems = [
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record.id),
              tooltip: t('common:edit')
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions')}
              iconSize="sm"
              iconVariant="default"
            />
          );
        },
      },
    ],
    [t, handleEdit]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AudienceFilterParams => {
    const queryParams: AudienceFilterParams = {
      page: params.page,
      limit: params.pageSize,
      ...(params.searchTerm && { search: params.searchTerm }),
      ...(params.sortBy && { sortBy: params.sortBy }),
      ...(params.sortDirection && { sortDirection: params.sortDirection }),
    };

    // Bỏ filter theo status vì không còn trường này

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<Audience, AudienceFilterParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách audiences với queryParams từ dataTable
  const { data: audienceData, isLoading } = useAudiences(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    // Chuyển đổi values thành AudienceFormData
    const audienceData: AudienceFormData = {
      name: values['name'] as string,
      email: values['email'] as string,
      phone: values['phone'] as string,
    };

    if (values['countryCode']) {
      audienceData.countryCode = values['countryCode'] as string;
    }

    if (values['tagIds']) {
      audienceData.tagIds = values['tagIds'] as number[];
    }

    createAudienceMutation.mutate(audienceData);
    hideAddForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý submit form sửa
  const handleEditSubmit = async (values: Record<string, unknown>) => {
    if (!editingAudienceId) return;

    try {
      // Chuyển đổi values thành AudienceFormData
      const audienceData: AudienceFormData = {
        name: values['name'] as string,
        email: values['email'] as string,
        phone: values['phone'] as string,
      };

      if (values['countryCode']) {
        audienceData.countryCode = values['countryCode'] as string;
      }

      if (values['tagIds']) {
        audienceData.tagIds = values['tagIds'] as number[];
      }

      await updateAudienceMutation.mutateAsync({ id: editingAudienceId, data: audienceData });

      NotificationUtil.success({
        message: t('marketing:audience.updateSuccess', 'Cập nhật audience thành công'),
        duration: 3000,
      });

      hideEditForm();
      setEditingAudienceId(null);
    } catch {
      NotificationUtil.error({
        message: t('marketing:audience.updateError', 'Cập nhật audience thất bại'),
        duration: 3000,
      });
    }
  };

  // Xử lý hủy form sửa
  const handleEditCancel = () => {
    hideEditForm();
    setEditingAudienceId(null);
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      {dataTable.filter.selectedValue !== undefined && (
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue}
          filterLabel={getFilterLabel()}
          onClearFilter={handleClearFilter}
          dateRange={dataTable.dateRange.dateRange}
          onClearDateRange={handleClearDateRange}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
          onClearAll={handleClearAll}
        />
      )}

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AudienceForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      {/* Form sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingAudience && (
          <AudienceForm
            initialData={editingAudience}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={audienceData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: audienceData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: audienceData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:audience.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} audience đã chọn?', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default AudiencePage;
