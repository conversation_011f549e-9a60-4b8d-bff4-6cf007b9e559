/**
 * Types for audience custom field API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Custom field type enum
 */
export enum CustomFieldType {
  TEXT = 'string',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',

}

/**
 * Custom field status enum
 */
export enum CustomFieldStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Custom field option
 */
export interface CustomFieldOption {
  id: string;
  label: string;
  value: string;
}

/**
 * Custom field entity
 */
export interface CustomField {
  createdBy: number;
  displayName: string;
  fieldKey: string;
  description?: string;
  dataType: CustomFieldType | string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: CustomFieldOption[];
  defaultValue?: string | number | boolean | string[];
  createdAt?: string;
  updatedAt?: string;

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType;

  // Trường ảo để hỗ trợ tương thích với các component hiện tại
  id?: number;
}

/**
 * Create custom field request
 */
export interface CreateCustomFieldRequest {
  fieldKey: string;
  displayName: string;
  dataType: CustomFieldType | string;
  description?: string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  key?: string;
  type?: CustomFieldType | string;
}

/**
 * Update custom field request
 */
export interface UpdateCustomFieldRequest {
  displayName?: string;
  description?: string;
  dataType?: CustomFieldType | string;
  status?: CustomFieldStatus;
  isRequired?: boolean;
  options?: Omit<CustomFieldOption, 'id'>[];
  defaultValue?: string | number | boolean | string[];

  // Hỗ trợ tương thích ngược với API cũ
  name?: string;
  type?: CustomFieldType | string;
}

/**
 * Custom field response
 */
export type CustomFieldResponse = CustomField;

/**
 * Custom field list response
 */
export type CustomFieldListResponse = ApiResponseDto<PaginatedResult<CustomFieldResponse>>;

/**
 * Custom field detail response
 */
export type CustomFieldDetailResponse = ApiResponseDto<CustomFieldResponse>;

/**
 * Custom field query params
 */
export interface CustomFieldQueryParams {
  search?: string;
  type?: CustomFieldType;
  status?: CustomFieldStatus;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}

/**
 * Marketing Custom Field Data Types - Admin version
 */
export enum MarketingCustomFieldDataType {
  TEXT = 'string',
  NUMBER = 'integer',
  BOOLEAN = 'boolean',
  DATE = 'date',
  SELECT = 'select',
  OBJECT = 'object',
}

/**
 * Marketing Select Option
 */
export interface MarketingSelectOption {
  title: string;
  value: string;
}

/**
 * Marketing Custom Field Response
 */
export interface MarketingCustomFieldResponse {
  id: number;
  fieldKey: string;
  userId: string;
  displayName: string;
  dataType: MarketingCustomFieldDataType | string;
  description?: string;
  tags?: string[];
  config?: Record<string, unknown>;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Create Marketing Custom Field Request
 */
export interface CreateMarketingCustomFieldRequest {
  fieldKey: string;
  displayName: string;
  dataType: MarketingCustomFieldDataType | string;
  description?: string;
  tags?: string[];
  config?: Record<string, unknown>;
}

/**
 * Update Marketing Custom Field Request
 */
export interface UpdateMarketingCustomFieldRequest {
  fieldKey?: string;
  displayName?: string;
  dataType?: MarketingCustomFieldDataType | string;
  description?: string;
  tags?: string[];
  config?: Record<string, unknown>;
}
