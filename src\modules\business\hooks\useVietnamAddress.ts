import { useQuery } from '@tanstack/react-query';
import { VietnamAddressService } from '../services/vietnam-address.api';
import { VIETNAM_ADDRESS_QUERY_KEYS } from '../constants/vietnam-address-query-keys';

/**
 * Hook lấy danh sách tỉnh/thành phố
 */
export const useProvinces = () => {
  return useQuery({
    queryKey: VIETNAM_ADDRESS_QUERY_KEYS.provinces(),
    queryFn: VietnamAddressService.getProvinces,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    select: (data) => data.map(VietnamAddressService.provinceToOption),
  });
};

/**
 * Hook lấy danh sách quận/huyện theo mã tỉnh
 */
export const useDistricts = (provinceCode?: number) => {
  return useQuery({
    queryKey: VIETNAM_ADDRESS_QUERY_KEYS.district(provinceCode || 0),
    queryFn: () => VietnamAddressService.getDistricts(provinceCode!),
    enabled: !!provinceCode,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    select: (data) => data.map(VietnamAddressService.districtToOption),
  });
};

/**
 * Hook lấy danh sách phường/xã theo mã quận
 */
export const useWards = (districtCode?: number) => {
  return useQuery({
    queryKey: VIETNAM_ADDRESS_QUERY_KEYS.ward(districtCode || 0),
    queryFn: () => VietnamAddressService.getWards(districtCode!),
    enabled: !!districtCode,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 24 * 60 * 60 * 1000, // 24 hours
    select: (data) => data.map(VietnamAddressService.wardToOption),
  });
};

/**
 * Hook tìm mã tỉnh theo tên
 */
export const useProvinceCode = (provinceName?: string) => {
  const { data: provinces } = useProvinces();
  
  if (!provinceName || !provinces) return undefined;
  
  const province = provinces.find(p => p.value === provinceName);
  return province?.code;
};

/**
 * Hook tìm mã quận theo tên
 */
export const useDistrictCode = (provinceCode?: number, districtName?: string) => {
  const { data: districts } = useDistricts(provinceCode);
  
  if (!districtName || !districts) return undefined;
  
  const district = districts.find(d => d.value === districtName);
  return district?.code;
};

/**
 * Hook tổng hợp để lấy tất cả options cho form địa chỉ
 */
export const useAddressOptions = (selectedProvince?: string, selectedDistrict?: string) => {
  const provinceCode = useProvinceCode(selectedProvince);
  const districtCode = useDistrictCode(provinceCode, selectedDistrict);

  const provincesQuery = useProvinces();
  const districtsQuery = useDistricts(provinceCode);
  const wardsQuery = useWards(districtCode);

  return {
    provinces: {
      data: provincesQuery.data || [],
      isLoading: provincesQuery.isLoading,
      error: provincesQuery.error,
    },
    districts: {
      data: districtsQuery.data || [],
      isLoading: districtsQuery.isLoading,
      error: districtsQuery.error,
    },
    wards: {
      data: wardsQuery.data || [],
      isLoading: wardsQuery.isLoading,
      error: wardsQuery.error,
    },
  };
};
