import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Icon,
} from '@/shared/components/common';
import { ShippingDto, ShippingMethod, AddressDto, OrderItemDto } from '../../types/order.types';
import AddressSelector from '../shipping/AddressSelector';
import PreferredCarrierSelector from '../shipping/PreferredCarrierSelector';
import SingleCarrierCalculator from '../shipping/SingleCarrierCalculator';

import { DeliveryAddressDto } from '../../services/shipping-calculator.service';

interface PhysicalShippingFormProps {
  shipping?: ShippingDto;
  onShippingChange: (shipping: ShippingDto) => void;
  customerAddress?: AddressDto;
  shopId?: number;
  customerId?: number;
  selectedItems?: OrderItemDto[];
}

/**
 * Form vận chuyển cho sản phẩm vật lý
 */
const PhysicalShippingForm: React.FC<PhysicalShippingFormProps> = ({
  shipping,
  onShippingChange,
  customerAddress,
  shopId,
  customerId,
  selectedItems = [],
}) => {
  const { t } = useTranslation(['business', 'common']);

  // State cho địa chỉ giao hàng
  const [selectedAddress, setSelectedAddress] = React.useState<DeliveryAddressDto>();

  // State cho preferred carrier
  const [preferredCarrier, setPreferredCarrier] = React.useState<'GHN' | 'GHTK'>();

  // Xử lý khi phí vận chuyển được tính toán
  const handleShippingCalculated = (carrier: string, fee: number, serviceType?: string, estimatedDeliveryTime?: string) => {
    console.log('🚚 [PhysicalShippingForm] Shipping calculated:', { carrier, fee, serviceType, estimatedDeliveryTime });
    console.log('🚚 [PhysicalShippingForm] Selected address:', selectedAddress);

    // Cập nhật shipping info với thông tin từ calculation
    const shippingData: ShippingDto = {
      ...shipping,
      method: carrier === 'GHN' ? ShippingMethod.GHN : ShippingMethod.GHTK,
      fee: fee,
      serviceName: serviceType || 'Chuẩn',
      estimatedDelivery: estimatedDeliveryTime,
      fromAddress: {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
      toAddress: customerAddress || {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
      // Thêm addressId nếu có từ selectedAddress
      addressId: selectedAddress?.addressId,
    };

    console.log('🚚 [PhysicalShippingForm] Final shipping data:', shippingData);
    onShippingChange(shippingData);
  };



  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <Typography variant="h5" className="text-foreground font-semibold mb-2">
          {t('business:order.shippingSetup', 'Thiết lập vận chuyển')}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          {t('business:order.shippingSetupDesc', 'Hoàn thành các bước sau để thiết lập vận chuyển cho đơn hàng')}
        </Typography>
      </div>

      {/* Step 1: Địa chỉ giao hàng */}
      {shopId && selectedItems.length > 0 && (
        <Card className="bg-card border-border">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-primary text-primary-foreground">
                <Icon name="map-pin" size="md" />
              </div>
              <div className="flex-1">
                <Typography variant="h6" className="text-foreground font-semibold">
                  {t('business:order.deliveryAddress', 'Địa chỉ giao hàng')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:order.deliveryAddressDesc', 'Chọn hoặc thêm địa chỉ giao hàng cho đơn hàng')}
                </Typography>
              </div>

            </div>

            <AddressSelector
              {...(selectedAddress && { selectedAddress })}
              onAddressChange={setSelectedAddress}
              {...(customerId && { customerId })}
            />
          </div>
        </Card>
      )}

      {/* Step 2: Chọn nhà vận chuyển ưu tiên */}
      {selectedAddress && shopId && selectedItems.length > 0 && (
        <Card className="bg-card border-border">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-primary text-primary-foreground">
                <Icon name="truck" size="md" />
              </div>
              <div className="flex-1">
                <Typography variant="h6" className="text-foreground font-semibold">
                  {t('business:order.preferredCarrier', 'Nhà vận chuyển ưu tiên')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:order.preferredCarrierDesc', 'Chọn nhà vận chuyển để tính phí vận chuyển tự động')}
                </Typography>
              </div>

            </div>

            <PreferredCarrierSelector
              {...(preferredCarrier && { selectedCarrier: preferredCarrier })}
              onCarrierChange={setPreferredCarrier}
            />
          </div>
        </Card>
      )}

      {/* Step 3: Tính phí vận chuyển */}
      {selectedAddress && preferredCarrier && shopId && selectedItems.length > 0 && (
        <Card className="bg-card border-border">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="relative flex items-center justify-center w-12 h-12 rounded-xl bg-primary text-primary-foreground shadow-lg">
                {/* Shipping truck icon SVG */}
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="text-white"
                >
                  <path
                    d="M1 3H15V13H1V3Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    fill="none"
                  />
                  <path
                    d="M16 8H20L23 11V16H16V8Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    fill="none"
                  />
                  <circle
                    cx="5.5"
                    cy="18.5"
                    r="2.5"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                  />
                  <circle
                    cx="18.5"
                    cy="18.5"
                    r="2.5"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                  />
                  <path
                    d="M8 16H13"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  {/* Calculator overlay icon */}
                  <rect
                    x="10"
                    y="1"
                    width="8"
                    height="10"
                    rx="1"
                    fill="white"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    className="opacity-90"
                  />
                  <line
                    x1="12"
                    y1="3"
                    x2="16"
                    y2="3"
                    stroke="currentColor"
                    strokeWidth="1"
                  />
                  <circle
                    cx="12.5"
                    cy="5.5"
                    r="0.5"
                    fill="currentColor"
                  />
                  <circle
                    cx="15.5"
                    cy="5.5"
                    r="0.5"
                    fill="currentColor"
                  />
                  <circle
                    cx="12.5"
                    cy="7.5"
                    r="0.5"
                    fill="currentColor"
                  />
                  <circle
                    cx="15.5"
                    cy="7.5"
                    r="0.5"
                    fill="currentColor"
                  />
                  <rect
                    x="12"
                    y="8.5"
                    width="4"
                    height="1.5"
                    rx="0.5"
                    fill="currentColor"
                  />
                </svg>

                {/* Animated pulse effect */}
                <div className="absolute inset-0 rounded-xl bg-primary/20 animate-pulse"></div>
              </div>
              <div className="flex-1">
                <Typography variant="h6" className="text-foreground font-semibold">
                  {t('business:order.shippingFeeCalculation', 'Tính phí vận chuyển')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('business:order.shippingFeeDesc', 'Hệ thống sẽ tự động tính phí vận chuyển dựa trên thông tin đã chọn')}
                </Typography>
              </div>
            </div>

            <SingleCarrierCalculator
              shopId={shopId}
              {...(customerId && { customerId })}
              selectedItems={selectedItems}
              {...(selectedAddress && { deliveryAddress: selectedAddress })}
              preferredCarrier={preferredCarrier}
              onShippingCalculated={handleShippingCalculated}
            />
          </div>
        </Card>
      )}
    </div>
  );
};

export default PhysicalShippingForm;
