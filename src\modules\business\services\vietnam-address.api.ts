import axios from 'axios';
import {
  Province,
  District,
  Ward,
  AddressSelectOption,
} from '../types/vietnam-address.types';

// Tạo axios instance riêng cho external API
const vietnamAddressClient = axios.create({
  baseURL: 'https://provinces.open-api.vn/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * API Service cho địa chỉ Việt Nam từ provinces.open-api.vn
 */
export class VietnamAddressService {
  /**
   * Lấy danh sách tỉnh/thành phố
   */
  static async getProvinces(): Promise<Province[]> {
    try {
      const response = await vietnamAddressClient.get<Province[]>('/p/');
      return response.data;
    } catch (error) {
      console.error('Error fetching provinces:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách quận/huyện theo mã tỉnh
   */
  static async getDistricts(provinceCode: number): Promise<District[]> {
    try {
      const response = await vietnamAddressClient.get<Province>(`/p/${provinceCode}?depth=2`);
      return response.data.districts || [];
    } catch (error) {
      console.error('Error fetching districts:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách phường/xã theo mã quận
   */
  static async getWards(districtCode: number): Promise<Ward[]> {
    try {
      const response = await vietnamAddressClient.get<District>(`/d/${districtCode}?depth=2`);
      return response.data.wards || [];
    } catch (error) {
      console.error('Error fetching wards:', error);
      throw error;
    }
  }

  /**
   * Chuyển đổi Province thành AddressSelectOption
   */
  static provinceToOption(province: Province): AddressSelectOption {
    return {
      value: province.name,
      label: province.name,
      code: province.code,
    };
  }

  /**
   * Chuyển đổi District thành AddressSelectOption
   */
  static districtToOption(district: District): AddressSelectOption {
    return {
      value: district.name,
      label: district.name,
      code: district.code,
    };
  }

  /**
   * Chuyển đổi Ward thành AddressSelectOption
   */
  static wardToOption(ward: Ward): AddressSelectOption {
    return {
      value: ward.name,
      label: ward.name,
      code: ward.code,
    };
  }
}
