import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography } from '@/shared/components/common';
import { ProductTypeEnum } from '../../types/product.types';
import {
  OrderItemDto,
  ShippingDto,
  DigitalDeliveryDto,
  OrderCustomerDto
} from '../../types/order.types';
import type {
  ServiceDeliveryInfo,
  EventDeliveryInfo
} from '../../types/delivery.types';
import PhysicalShippingForm from './PhysicalShippingForm';
import DigitalDeliveryForm from './DigitalDeliveryForm';
import ServiceDeliveryForm from './ServiceDeliveryForm';
import EventDeliveryForm from './EventDeliveryForm';
import { useComboShipping } from '../../hooks/useComboShipping';

interface DeliveryFormManagerProps {
  selectedItems: OrderItemDto[];
  selectedCustomer: OrderCustomerDto | undefined;
  shipping: ShippingDto | undefined;
  digitalDelivery: DigitalDeliveryDto | undefined;
  onShippingChange: (shipping: ShippingDto) => void;
  onDigitalDeliveryChange: (delivery: DigitalDeliveryDto) => void;
  onServiceInfoChange?: (info: ServiceDeliveryInfo) => void;
  onEventInfoChange?: (info: EventDeliveryInfo) => void;
  shopId?: number; // ID shop để tính phí vận chuyển
}

/**
 * Component quản lý các form giao hàng/vận chuyển dựa trên loại sản phẩm
 */
const DeliveryFormManager: React.FC<DeliveryFormManagerProps> = ({
  selectedItems,
  selectedCustomer,
  shipping,
  digitalDelivery,
  onShippingChange,
  onDigitalDeliveryChange,
  onServiceInfoChange,
  onEventInfoChange,
  shopId,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Wrapper functions to handle type conversion
  const handleServiceInfoChange = (info: ServiceDeliveryInfo) => {
    onServiceInfoChange?.(info);
  };

  const handleEventInfoChange = (info: EventDeliveryInfo) => {
    onEventInfoChange?.(info);
  };

  // Phân tích loại sản phẩm trong đơn hàng
  const productTypeAnalysis = useMemo(() => {
    const types = new Set<ProductTypeEnum>();
    
    selectedItems.forEach(item => {
      if (item.productType) {
        types.add(item.productType);
      } else {
        // Mặc định là PHYSICAL nếu không có productType
        types.add(ProductTypeEnum.PHYSICAL);
      }
    });

    return {
      types: Array.from(types),
      hasPhysical: types.has(ProductTypeEnum.PHYSICAL),
      hasDigital: types.has(ProductTypeEnum.DIGITAL),
      hasService: types.has(ProductTypeEnum.SERVICE),
      hasEvent: types.has(ProductTypeEnum.EVENT),
      hasCombo: types.has(ProductTypeEnum.COMBO),
      isMultipleTypes: types.size > 1,
    };
  }, [selectedItems]);

  // Sử dụng hook để kiểm tra shipping needs (bao gồm combo)
  const { needsShipping: shouldShowPhysicalShipping, isLoading: checkingShipping } = useComboShipping(selectedItems);
  
  const shouldShowDigitalDelivery = productTypeAnalysis.hasDigital;
  const shouldShowServiceDelivery = productTypeAnalysis.hasService;
  const shouldShowEventDelivery = productTypeAnalysis.hasEvent;

  // Nếu không có sản phẩm nào được chọn
  if (selectedItems.length === 0) {
    return (
      <div className="text-center py-8">
        <Typography variant="body1" className="text-gray-500">
          {t('business:order.selectProductsFirst')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Hiển thị thông tin về loại sản phẩm */}
      {productTypeAnalysis.isMultipleTypes && (
        <div className="p-4 bg-blue-50 rounded-lg">
          <Typography variant="subtitle2" className="mb-2 text-blue-800">
            {t('business:order.multipleProductTypes')}
          </Typography>
          <Typography variant="body2" className="text-blue-600">
            {t('business:order.multipleProductTypesDescription')}
          </Typography>
        </div>
      )}

      {/* Form vận chuyển sản phẩm vật lý */}
      {checkingShipping ? (
        <div className="p-4 bg-blue-50 rounded-lg">
          <Typography variant="body2" className="text-blue-800">
            {t('business:order.checkingShippingNeeds', 'Đang kiểm tra nhu cầu vận chuyển...')}
          </Typography>
        </div>
      ) : shouldShowPhysicalShipping ? (
        <PhysicalShippingForm
          {...(shipping && { shipping })}
          onShippingChange={onShippingChange}
          {...(selectedCustomer?.address && { customerAddress: selectedCustomer.address })}
          {...(shopId && { shopId })}
          {...(selectedCustomer?.id && { customerId: selectedCustomer.id })}
          selectedItems={selectedItems}
        />
      ) : null}

      {/* Form giao hàng sản phẩm số */}
      {shouldShowDigitalDelivery && (
        <DigitalDeliveryForm
          {...(digitalDelivery && { delivery: digitalDelivery })}
          onDeliveryChange={onDigitalDeliveryChange}
          {...(selectedCustomer?.email && { customerEmail: selectedCustomer.email })}
          {...(selectedCustomer?.phone && { customerPhone: selectedCustomer.phone })}
        />
      )}

      {/* Form dịch vụ */}
      {shouldShowServiceDelivery && onServiceInfoChange && (
        <ServiceDeliveryForm
          onServiceInfoChange={handleServiceInfoChange}
          {...(selectedCustomer?.name && { customerName: selectedCustomer.name })}
          {...(selectedCustomer?.phone && { customerPhone: selectedCustomer.phone })}
        />
      )}

      {/* Form sự kiện */}
      {shouldShowEventDelivery && onEventInfoChange && (
        <EventDeliveryForm
          onEventInfoChange={handleEventInfoChange}
          {...(selectedCustomer?.name && { customerName: selectedCustomer.name })}
          {...(selectedCustomer?.phone && { customerPhone: selectedCustomer.phone })}
          {...(selectedCustomer?.email && { customerEmail: selectedCustomer.email })}
        />
      )}

      {/* Thông tin tóm tắt */}
      <div className="p-4 rounded-lg">
        <Typography variant="subtitle2" className="mb-2">
          {t('business:order.deliverySummary')}
        </Typography>
        <div className="space-y-1">
          {shouldShowPhysicalShipping && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.physicalShippingRequired')}
            </Typography>
          )}
          {shouldShowDigitalDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.digitalDeliveryRequired')}
            </Typography>
          )}
          {shouldShowServiceDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.serviceDeliveryRequired')}
            </Typography>
          )}
          {shouldShowEventDelivery && (
            <Typography variant="body2" className="text-gray-600">
              • {t('business:order.eventDeliveryRequired')}
            </Typography>
          )}
        </div>
      </div>
    </div>
  );
};

export default DeliveryFormManager;
