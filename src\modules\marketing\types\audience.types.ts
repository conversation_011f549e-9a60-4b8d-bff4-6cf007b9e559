/**
 * Types for audience API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Audience status enum
 */
export enum AudienceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Audience type enum
 */
export enum AudienceType {
  CUSTOMER = 'customer',
  LEAD = 'lead',
  SUBSCRIBER = 'subscriber',
  CUSTOM = 'custom',
}

/**
 * Audience attribute
 */
export interface AudienceAttribute {
  id: string;
  name: string;
  value: string;
}

/**
 * Audience entity
 */
export interface Audience {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  countryCode?: string;
  tagIds?: number[];
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes?: AudienceAttribute[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Create audience request
 */
export interface CreateAudienceRequest {
  name: string;
  email?: string;
  phone?: string;
  countryCode?: string;
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}

/**
 * Update audience request
 */
export interface UpdateAudienceRequest {
  name?: string;
  email?: string;
  phone?: string;
  countryCode?: string;
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}

/**
 * Audience response
 */
export type AudienceResponse = Audience;

/**
 * Contact data structure from API (actual response)
 */
export interface ContactData {
  id: string;
  email?: string;
  phone?: string;
  customFields?: Array<{
    name?: string;
    value?: string;
  }>;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Actual API response structure for audience list (returns contact data)
 */
export interface ActualAudienceListResult {
  data: ContactData[];
  meta?: {
    totalItems?: number;
    itemCount?: number;
    itemsPerPage?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

/**
 * Audience list response (actual API structure)
 */
export type AudienceListResponse = ApiResponseDto<ActualAudienceListResult>;

/**
 * Audience detail response
 */
export type AudienceDetailResponse = ApiResponseDto<AudienceResponse>;

/**
 * Audience query params
 */
export interface AudienceQueryParams {
  search?: string | undefined;
  status?: AudienceStatus;
  type?: AudienceType;
  page?: number;
  limit?: number;
  sortBy?: string | undefined;
  sortDirection?: string | undefined;
}
