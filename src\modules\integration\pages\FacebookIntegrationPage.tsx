import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Modal,
  Pagination,
  ResponsiveGrid,
  Container,
  EmptyState,
  Tooltip,
  Chip,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  useGetFacebookPages,
  useDeleteFacebookPage,
  useCreateFacebookAuthUrl,
  useHandleFacebookCallback,
} from '../facebook/hooks/useFacebook';
import { FacebookPageDto, FacebookPageQueryDto } from '../facebook/types/facebook.types';

// Facebook Page Card Component
interface FacebookPageCardProps {
  page: FacebookPageDto;
  onDelete: (pageId: string) => void;
}

const FacebookPageCard: React.FC<FacebookPageCardProps> = ({
  page,
  onDelete,
}) => {
  const { t } = useTranslation('integration');

  const getStatusIcon = (isActive: boolean, isError: boolean) => {
    if (isError) return 'alert-circle-filled';
    if (isActive) return 'rosette-discount-check';
    return 'clock';
  };

  const getStatusColor = (isActive: boolean, isError: boolean) => {
    if (isError) return 'text-red-500';
    if (isActive) return 'text-green-500';
    return 'text-gray-500';
  };

  const getStatusTooltip = (isActive: boolean, isError: boolean) => {
    if (isError) return t('integration.facebook.status.error', 'Có lỗi xảy ra - Kiểm tra kết nối');
    if (isActive) return t('integration.facebook.status.active', 'Đang hoạt động bình thường');
    return t('integration.facebook.status.inactive', 'Tạm dừng hoạt động');
  };



  return (
    <Card className="p-3 sm:p-4 h-full flex flex-col relative transition-all duration-200 hover:shadow-md dark:hover:shadow-lg dark:hover:shadow-gray-900/20">
      {/* Delete button ở góc trên bên phải */}
      <div
        className="absolute top-2 right-2 sm:top-3 sm:right-3 z-10 w-7 h-7 sm:w-8 sm:h-8 flex items-center justify-center rounded-full cursor-pointer transition-all duration-200 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 hover:scale-110"
        onClick={() => onDelete(page.facebookPageId)}
      >
        <Tooltip content={t('common.delete', 'Xóa')} position="bottom">
          <Icon name="trash" size="sm" />
        </Tooltip>
      </div>

      <div className="flex items-start space-x-2 sm:space-x-3 mb-3 pr-6 sm:pr-8">
        <div className="relative shrink-0">
          {/* Hiển thị avatar page hoặc fallback về icon Facebook */}
          {page.avatarPage ? (
            <img
              src={page.avatarPage}
              alt={page.pageName}
              className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover ring-2 ring-gray-100 dark:ring-gray-700"
              onError={(e) => {
                // Fallback về icon Facebook nếu ảnh lỗi
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 bg-[#1877F2] dark:bg-[#1565C0] rounded-full ring-2 ring-gray-100 dark:ring-gray-700 ${page.avatarPage ? 'hidden' : ''}`}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-4 h-4 sm:w-6 sm:h-6 text-white"
            >
              <path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96C18.34 21.21 22 17.06 22 12.06C22 6.53 17.5 2.04 12 2.04Z" />
            </svg>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-start sm:items-center justify-between gap-2 mb-1">
            <Typography variant="h6" className="font-semibold text-sm sm:text-base truncate leading-tight">
              {page.pageName}
            </Typography>
            {/* Icon trạng thái hoạt động */}
            <Tooltip content={getStatusTooltip(page.isActive, page.isError)} position="top">
              <Icon
                name={getStatusIcon(page.isActive, page.isError)}
                size="sm"
                className={`${getStatusColor(page.isActive, page.isError)} shrink-0`}
              />
            </Tooltip>
          </div>
          <Typography variant="body2" color="muted" className="text-xs sm:text-sm truncate">
            {t('integration.facebook.owner', 'Chủ sở hữu')}: {page.facebookPersonalName}
          </Typography>
        </div>
      </div>

      <div className="flex-1 mt-2 sm:mt-3">
        {/* Hiển thị tên agent trong Chip */}
        {page.agent && (
          <div className="flex items-center justify-between">
            <Chip
              variant="secondary"
              size="sm"
              className="text-xs max-w-full"
            >
              <span className="truncate">{page.agent.name}</span>
            </Chip>
            <Typography variant="body2" color="muted" className="text-xs ml-2 shrink-0">
              {t('integration.facebook.agent', 'Agent')}
            </Typography>
          </div>
        )}
        {!page.agent && (
          <Typography variant="body2" color="muted" className="text-xs italic">
            {t('integration.facebook.noAgent', 'Chưa kết nối Agent')}
          </Typography>
        )}
      </div>
    </Card>
  );
};

/**
 * Trang hiển thị danh sách Facebook Pages đã liên kết
 */
const FacebookIntegrationPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);

  // Query parameters
  const queryParams = useMemo((): FacebookPageQueryDto => ({
    page: currentPage,
    limit: itemsPerPage,
    ...(searchKeyword && { keyword: searchKeyword }),
  }), [currentPage, itemsPerPage, searchKeyword]);

  // Get current URL for redirect
  const currentUrl = window.location.origin + window.location.pathname;

  // API hooks
  const { data: pagesData, isLoading, error, refetch } = useGetFacebookPages(queryParams);
  const deletePageMutation = useDeleteFacebookPage();

  // Facebook Auth hooks - We'll use refetch manually when needed
  const authUrlQuery = useCreateFacebookAuthUrl(
    { redirectUrl: currentUrl }
  );
  const handleCallbackMutation = useHandleFacebookCallback();

  const pages = pagesData?.result?.items || [];
  const totalItems = pagesData?.result?.meta?.totalItems || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Handle Facebook callback when page loads with code parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state && !handleCallbackMutation.isPending) {
      setIsConnecting(true);

      handleCallbackMutation.mutate(
        {
          code,
          redirectUri: currentUrl,
          state
        },
        {
          onSuccess: () => {
            // Remove code from URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('code');
            newUrl.searchParams.delete('state');
            window.history.replaceState({}, '', newUrl.toString());

            setIsConnecting(false);
            // Pages will be automatically refetched due to query invalidation
          },
          onError: (error) => {
            console.error('Facebook callback error:', error);
            setIsConnecting(false);
          }
        }
      );
    }
  }, [handleCallbackMutation, currentUrl, setIsConnecting]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle delete page confirmation
  const handleDeleteConfirm = (pageId: string) => {
    setPageToDelete(pageId);
    setShowDeleteConfirm(true);
  };

  // Handle delete page
  const handleDeletePage = async () => {
    if (!pageToDelete) return;

    try {
      await deletePageMutation.mutateAsync(pageToDelete);
      setShowDeleteConfirm(false);
      setPageToDelete(null);
      // Success notification will be handled by the mutation
    } catch (error) {
      console.error('Error removing page:', error);
      // Error notification will be handled by the mutation
    }
  };

  // Cancel delete
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setPageToDelete(null);
  };



  // Handle search
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle retry
  const handleRetry = () => {
    refetch();
  };

  // Handle connect Facebook
  const handleConnectFacebook = async () => {
    try {
      setIsConnecting(true);

      // Check if we already have auth URL data
      if (authUrlQuery.data?.result?.authUrl) {
        // Redirect to Facebook auth
        window.location.href = authUrlQuery.data.result.authUrl;
      } else {
        // Fetch auth URL
        const authResponse = await authUrlQuery.refetch();

        if (authResponse.data?.result?.authUrl) {
          // Redirect to Facebook auth
          window.location.href = authResponse.data.result.authUrl;
        } else {
          console.error('No auth URL received');
          setIsConnecting(false);
        }
      }
    } catch (error) {
      console.error('Error getting Facebook auth URL:', error);
      setIsConnecting(false);
    }
  };

  return (
    <Container className="px-4 sm:px-6 lg:px-8">
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
          <div className="flex items-center space-x-2 sm:space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="flex items-center space-x-1 sm:space-x-2 shrink-0"
            >
              <Icon name="arrow-left" size="sm" />
              <span className="hidden sm:inline">{t('common.back', 'Quay lại')}</span>
            </Button>
            <Typography variant="h4" className="text-lg sm:text-xl lg:text-2xl truncate">
              {t('integration.facebook.title', 'Facebook')}
            </Typography>
          </div>
        </div>
        <Typography variant="body1" color="muted" className="text-sm sm:text-base">
          {t('integration.facebook.description', 'Quản lý các tài khoản Facebook đã liên kết với hệ thống')}
        </Typography>
      </div>

      {/* Menu Icon Bar với tìm kiếm và thêm mới */}
      {pages.length > 0 && (
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleConnectFacebook}
          isLoading={isConnecting}
        />
      )}

      {isLoading || isConnecting ? (
        <div className="flex flex-col justify-center items-center py-12 sm:py-16">
          <div className="w-8 h-8 sm:w-10 sm:h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          {isConnecting && (
            <Typography className="mt-3 text-sm sm:text-base text-center">
              {t('integration.facebook.processing', 'Đang xử lý kết nối Facebook...')}
            </Typography>
          )}
        </div>
      ) : error ? (
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Đã xảy ra lỗi')}
          description={t('integration.facebook.loadError', 'Không thể tải danh sách Facebook Pages. Vui lòng thử lại.')}
          actions={
            <Button variant="outline" onClick={handleRetry}>
              <Icon name="refresh" size="sm" className="mr-2" />
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      ) : pages.length === 0 ? (
        <EmptyState
          icon="facebook"
          title={t('integration.facebook.noPages', 'Chưa có Facebook Page nào')}
          description={t('integration.facebook.noPagesDescription', 'Bạn chưa liên kết Facebook Page nào với hệ thống. Hãy thêm Facebook Page để bắt đầu sử dụng tính năng tích hợp.')}
          actions={
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="arrow-left" size="sm" />
                <span>{t('common.back', 'Quay lại')}</span>
              </Button>
              <Button
                variant="primary"
                onClick={handleConnectFacebook}
                isLoading={isConnecting}
                disabled={isConnecting}
                className="flex items-center justify-center space-x-2"
              >
                <Icon name="plus" size="sm" />
                <span>
                  {isConnecting
                    ? t('integration.facebook.connecting', 'Đang kết nối...')
                    : t('integration.facebook.addPage', 'Thêm Facebook Page')
                  }
                </span>
              </Button>
            </div>
          }
        />
      ) : (
        <div className="space-y-4 sm:space-y-6">
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }} className="gap-4 sm:gap-6">
            {pages.map(page => (
              <div key={page.facebookPageId} className="h-full">
                <FacebookPageCard
                  page={page}
                  onDelete={handleDeleteConfirm}
                />
              </div>
            ))}
          </ResponsiveGrid>

          {/* Chỉ hiển thị pagination khi có nhiều hơn 1 trang */}
          {totalPages > 1 && (
            <div className="mt-6 flex flex-col sm:flex-row sm:justify-between items-center gap-4">
              <Typography variant="body2" color="muted" className="text-xs sm:text-sm">
                {t('common.showing', 'Hiển thị')} {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, totalItems)} {t('common.of', 'trong tổng số')} {totalItems} {t('integration.facebook.pages', 'trang')}
              </Typography>
              <Pagination
                variant="compact"
                borderless={true}
                currentPage={currentPage}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
                className="justify-center sm:justify-end"
              />
            </div>
          )}
        </div>
      )}

      {/* Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirm', 'Xác nhận xóa')}
        footer={
          <div className="flex flex-col sm:flex-row justify-end gap-2 sm:gap-3">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              className="w-full sm:w-auto order-2 sm:order-1"
            >
              <Icon name="x" size="sm" className="mr-2" />
              {t('common.cancel', 'Hủy bỏ')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeletePage}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              <Icon name="trash" size="sm" className="mr-2" />
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="p-4 sm:p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <Icon name="alert-circle" size="md" className="text-red-500" />
            </div>
            <div className="flex-1">
              <Typography variant="body1" className="mb-2">
                {t('integration.facebook.confirmDelete', 'Bạn có chắc chắn muốn xóa Facebook Page này khỏi hệ thống?')}
              </Typography>
              <Typography variant="body2" color="muted" className="text-sm">
                {t('integration.facebook.confirmDeleteDescription', 'Hành động này không thể hoàn tác. Tất cả dữ liệu liên quan sẽ bị xóa vĩnh viễn.')}
              </Typography>
            </div>
          </div>
        </div>
      </Modal>
    </Container>
  );
};

export default FacebookIntegrationPage;
