import React, { useState, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Modal,
  Pagination,
  ResponsiveGrid,
  Container,
  EmptyState,
  Tooltip,
} from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  useGetFacebookPages,
  useDeleteFacebookPage,
  useCreateFacebookAuthUrl,
  useHandleFacebookCallback,
} from '../facebook/hooks/useFacebook';
import { FacebookPageDto, FacebookPageQueryDto } from '../facebook/types/facebook.types';

// Facebook Page Card Component
interface FacebookPageCardProps {
  page: FacebookPageDto;
  onDelete: (pageId: string) => void;
}

const FacebookPageCard: React.FC<FacebookPageCardProps> = ({
  page,
  onDelete,
}) => {
  const { t } = useTranslation('integration');

  const getStatusIcon = (isActive: boolean, isError: boolean) => {
    if (isError) return 'alert-circle';
    if (isActive) return 'check-circle';
    return 'clock';
  };

  const getStatusColor = (isActive: boolean, isError: boolean) => {
    if (isError) return 'text-red-500';
    if (isActive) return 'text-green-500';
    return 'text-gray-500';
  };

  const getStatusTooltip = (isActive: boolean, isError: boolean) => {
    if (isError) return t('facebook.status.error', 'Lỗi');
    if (isActive) return t('facebook.status.active', 'Hoạt động');
    return t('facebook.status.inactive', 'Không hoạt động');
  };

  const getAgentStatusIcon = () => {
    if (page.agent) return 'user-check';
    return 'user-x';
  };

  const getAgentStatusColor = () => {
    if (page.agent) return 'text-blue-500';
    return 'text-gray-400';
  };

  const getAgentStatusTooltip = () => {
    if (page.agent) return t('facebook.agent.connected', `Đã kết nối với ${page.agent.name}`);
    return t('facebook.agent.notConnected', 'Chưa kết nối Agent');
  };

  return (
    <Card className="p-4 h-full flex flex-col relative">
      {/* Delete button ở góc trên bên phải */}
      <div className="absolute top-3 right-3 z-10">
        <Tooltip content={t('common.delete', 'Xóa')} position="bottom">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(page.facebookPageId)}
            className="text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 p-1.5 rounded-full transition-colors"
          >
            <Icon name="trash-2" size="sm" />
          </Button>
        </Tooltip>
      </div>

      <div className="flex items-start space-x-3 mb-3 pr-8">
        <div className="relative">
          {/* Hiển thị avatar page hoặc fallback về icon Facebook */}
          {page.avatarPage ? (
            <img
              src={page.avatarPage}
              alt={page.pageName}
              className="w-10 h-10 rounded-full object-cover"
              onError={(e) => {
                // Fallback về icon Facebook nếu ảnh lỗi
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`flex items-center justify-center w-10 h-10 bg-[#1877F2] rounded-full ${page.avatarPage ? 'hidden' : ''}`}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-6 h-6 text-white"
            >
              <path d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96C18.34 21.21 22 17.06 22 12.06C22 6.53 17.5 2.04 12 2.04Z" />
            </svg>
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <Typography variant="h6" className="font-semibold truncate">
            {page.pageName}
          </Typography>
          <Typography variant="body2" color="muted" className="text-sm">
            {page.facebookPersonalName}
          </Typography>
        </div>
      </div>

      <div className="flex-1">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            {/* Icon trạng thái hoạt động */}
            <Tooltip content={getStatusTooltip(page.isActive, page.isError)} position="top">
              <Icon
                name={getStatusIcon(page.isActive, page.isError)}
                size="sm"
                className={getStatusColor(page.isActive, page.isError)}
              />
            </Tooltip>

            {/* Icon trạng thái Agent */}
            <Tooltip content={getAgentStatusTooltip()} position="top">
              <Icon
                name={getAgentStatusIcon()}
                size="sm"
                className={getAgentStatusColor()}
              />
            </Tooltip>
          </div>

          {page.agent && (
            <Typography variant="body2" className="font-medium text-sm">
              {page.agent.name}
            </Typography>
          )}
        </div>
      </div>
    </Card>
  );
};

/**
 * Trang hiển thị danh sách Facebook Pages đã liên kết
 */
const FacebookIntegrationPage: React.FC = () => {
  const { t } = useTranslation('integration');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [pageToDelete, setPageToDelete] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);

  // Query parameters
  const queryParams = useMemo((): FacebookPageQueryDto => ({
    page: currentPage,
    limit: itemsPerPage,
    ...(searchKeyword && { keyword: searchKeyword }),
  }), [currentPage, itemsPerPage, searchKeyword]);

  // Get current URL for redirect
  const currentUrl = window.location.origin + window.location.pathname;

  // API hooks
  const { data: pagesData, isLoading, error, refetch } = useGetFacebookPages(queryParams);
  const deletePageMutation = useDeleteFacebookPage();

  // Facebook Auth hooks - We'll use refetch manually when needed
  const authUrlQuery = useCreateFacebookAuthUrl(
    { redirectUrl: currentUrl }
  );
  const handleCallbackMutation = useHandleFacebookCallback();

  const pages = pagesData?.result?.items || [];
  const totalItems = pagesData?.result?.meta?.totalItems || 0;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Handle Facebook callback when page loads with code parameter
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state && !handleCallbackMutation.isPending) {
      setIsConnecting(true);

      handleCallbackMutation.mutate(
        {
          code,
          redirectUri: currentUrl,
          state
        },
        {
          onSuccess: () => {
            // Remove code from URL
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete('code');
            newUrl.searchParams.delete('state');
            window.history.replaceState({}, '', newUrl.toString());

            setIsConnecting(false);
            // Pages will be automatically refetched due to query invalidation
          },
          onError: (error) => {
            console.error('Facebook callback error:', error);
            setIsConnecting(false);
          }
        }
      );
    }
  }, [handleCallbackMutation, currentUrl, setIsConnecting]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Handle delete page confirmation
  const handleDeleteConfirm = (pageId: string) => {
    setPageToDelete(pageId);
    setShowDeleteConfirm(true);
  };

  // Handle delete page
  const handleDeletePage = async () => {
    if (!pageToDelete) return;

    try {
      await deletePageMutation.mutateAsync(pageToDelete);
      setShowDeleteConfirm(false);
      setPageToDelete(null);
      // Success notification will be handled by the mutation
    } catch (error) {
      console.error('Error removing page:', error);
      // Error notification will be handled by the mutation
    }
  };

  // Cancel delete
  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setPageToDelete(null);
  };



  // Handle search
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle retry
  const handleRetry = () => {
    refetch();
  };

  // Handle connect Facebook
  const handleConnectFacebook = async () => {
    try {
      setIsConnecting(true);

      // Check if we already have auth URL data
      if (authUrlQuery.data?.result?.authUrl) {
        // Redirect to Facebook auth
        window.location.href = authUrlQuery.data.result.authUrl;
      } else {
        // Fetch auth URL
        const authResponse = await authUrlQuery.refetch();

        if (authResponse.data?.result?.authUrl) {
          // Redirect to Facebook auth
          window.location.href = authResponse.data.result.authUrl;
        } else {
          console.error('No auth URL received');
          setIsConnecting(false);
        }
      }
    } catch (error) {
      console.error('Error getting Facebook auth URL:', error);
      setIsConnecting(false);
    }
  };

  return (
    <Container>
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="flex items-center space-x-2"
            >
              <Icon name="arrow-left" size="sm" />
              <span>{t('common.back', 'Quay lại')}</span>
            </Button>
            <Typography variant="h4">
              {t('social.title', 'Tích hợp mạng xã hội')} - Facebook
            </Typography>
          </div>
        </div>
        <Typography variant="body1" color="muted">
          {t('social.description', 'Quản lý các tài khoản Facebook đã liên kết')}
        </Typography>
      </div>

      {/* Menu Icon Bar với tìm kiếm và thêm mới */}
      {pages.length > 0 && (
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleConnectFacebook}
          isLoading={isConnecting}
        />
      )}

      {isLoading || isConnecting ? (
        <div className="flex justify-center items-center py-12">
          <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          {isConnecting && (
            <Typography className="ml-3">
              {t('facebook.processing', 'Đang xử lý kết nối Facebook...')}
            </Typography>
          )}
        </div>
      ) : error ? (
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Đã xảy ra lỗi')}
          description={t('facebook.loadError', 'Không thể tải danh sách Facebook Pages')}
          actions={
            <Button variant="outline" onClick={handleRetry}>
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      ) : pages.length === 0 ? (
        <EmptyState
          icon="facebook"
          title={t('facebook.noPages', 'Chưa có Facebook Page nào')}
          description={t('facebook.noPagesDescription', 'Bạn chưa liên kết Facebook Page nào. Hãy thêm Facebook Page để bắt đầu.')}
          actions={
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="flex items-center space-x-2"
              >
                <Icon name="arrow-left" size="sm" />
                <span>{t('common.back', 'Quay lại')}</span>
              </Button>
              <Button
                variant="primary"
                onClick={handleConnectFacebook}
                isLoading={isConnecting}
                disabled={isConnecting}
              >
                {isConnecting
                  ? t('facebook.connecting', 'Đang kết nối...')
                  : t('facebook.addPage', 'Thêm Facebook Page')
                }
              </Button>
            </div>
          }
        />
      ) : (
        <div>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }}>
            {pages.map(page => (
              <div key={page.facebookPageId} className="h-full">
                <FacebookPageCard
                  page={page}
                  onDelete={handleDeleteConfirm}
                />
              </div>
            ))}
          </ResponsiveGrid>

          {/* Chỉ hiển thị pagination khi có nhiều hơn 1 trang */}
          {totalPages > 1 && (
            <div className="mt-6 flex justify-end">
              <Pagination
                variant="compact"
                borderless={true}
                currentPage={currentPage}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            </div>
          )}
        </div>
      )}

      {/* Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        title={t('common.confirm', 'Xác nhận')}
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelDelete}>
              {t('common.cancel', 'Hủy')}
            </Button>
            <Button variant="danger" onClick={handleDeletePage}>
              {t('common.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="p-4">
          <Typography>
            {t('facebook.confirmDelete', 'Bạn có chắc chắn muốn xóa Facebook Page này?')}
          </Typography>
        </div>
      </Modal>
    </Container>
  );
};

export default FacebookIntegrationPage;
