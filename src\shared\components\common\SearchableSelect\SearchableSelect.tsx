import { useState, useRef, useEffect, ReactNode, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';

import { Icon } from '@/shared/components/common';
import ScrollArea from '../ScrollArea';
import { Z_INDEX } from '@/shared/constants/breakpoints';

// Kiểu dữ liệu cho option
export interface SearchableSelectOption {
  value: string | number;
  label: string;
  disabled?: boolean;
  icon?: ReactNode;
  imageUrl?: string;
  data?: Record<string, unknown>;
}

export interface SearchableSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | number;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | number) => void;

  /**
   * Options
   */
  options: SearchableSelectOption[];

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Loading
   */
  loading?: boolean;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Custom search function
   */
  onSearch?: (searchText: string) => void;

  /**
   * Allow clear selection
   */
  clearable?: boolean;
}

/**
 * Component SearchableSelect với keyboard navigation và search tích hợp
 */
const SearchableSelect = forwardRef<HTMLInputElement, SearchableSelectProps>(
  (
    {
      value,
      onChange,
      options = [],
      placeholder = '',
      label,
      disabled = false,
      loading = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      onSearch,
      clearable = false,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme();
    
    const [isOpen, setIsOpen] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number; width: number } | null>(null);
    
    const selectRef = useRef<HTMLDivElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const hiddenInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to hidden input element
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Calculate dropdown position
    const calculateDropdownPosition = () => {
      if (!selectRef.current) return null;

      const rect = selectRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      return {
        top: rect.bottom + scrollTop + 4,
        left: rect.left + scrollLeft,
        width: rect.width,
      };
    };

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Filter options based on search text
    const filteredOptions = options.filter(option =>
      option.label.toLowerCase().includes(searchText.toLowerCase())
    );

    // Get selected option
    const selectedOption = options.find(option => option.value === value);

    // Get display value for input
    const getDisplayValue = () => {
      if (isOpen) return searchText;
      return selectedOption ? selectedOption.label : '';
    };

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newSearchText = e.target.value;
      setSearchText(newSearchText);
      setHighlightedIndex(-1);
      
      if (!isOpen) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
        setIsOpen(true);
      }

      if (onSearch) {
        onSearch(newSearchText);
      }
    };

    // Handle input focus
    const handleInputFocus = () => {
      if (!disabled && !loading) {
        const position = calculateDropdownPosition();
        setDropdownPosition(position);
        setIsOpen(true);
        setSearchText('');
      }
    };

    // Handle option selection
    const handleOptionSelect = (optionValue: string | number) => {
      const selectedOpt = options.find(opt => opt.value === optionValue);
      if (selectedOpt && onChange) {
        onChange(optionValue);
      }
      setIsOpen(false);
      setDropdownPosition(null);
      setSearchText('');
      setHighlightedIndex(-1);
      
      // Focus back to input
      if (inputRef.current) {
        inputRef.current.blur();
      }
    };

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setHighlightedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setHighlightedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
            const selectedOption = filteredOptions[highlightedIndex];
            if (!selectedOption.disabled) {
              handleOptionSelect(selectedOption.value);
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setIsOpen(false);
          setDropdownPosition(null);
          setSearchText('');
          setHighlightedIndex(-1);
          if (inputRef.current) {
            inputRef.current.blur();
          }
          break;
      }
    };

    // Handle clear selection
    const handleClear = (e: React.MouseEvent) => {
      e.stopPropagation();
      if (onChange) {
        onChange('');
      }
      setSearchText('');
    };

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;
        const isOutsideContainer = selectRef.current && !selectRef.current.contains(target);
        const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(target);

        if (isOutsideContainer && isOutsideDropdown) {
          setIsOpen(false);
          setDropdownPosition(null);
          setSearchText('');
          setHighlightedIndex(-1);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }
    }, [isOpen]);

    // Reset search text when value changes externally
    useEffect(() => {
      if (!isOpen) {
        setSearchText('');
      }
    }, [value, isOpen]);

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={value || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium text-foreground mb-1">{label}</label>}

        {/* Input field */}
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={getDisplayValue()}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={`
              w-full px-3 pr-10
              border-0 dark:border rounded-md bg-card-muted text-foreground
              ${sizeClasses}
              ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-text'}
              ${error ? 'dark:border-error' : 'dark:border-border'}
              ${isOpen ? 'ring-2 ring-primary/30' : ''}
              focus:outline-none focus:ring-2 focus:ring-primary/30
            `}
          />

          {/* Right icons */}
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {clearable && value && !disabled && (
              <button
                type="button"
                onClick={handleClear}
                className="p-1 hover:bg-muted rounded-sm transition-colors"
              >
                <Icon name="x" size="sm" className="text-muted-foreground hover:text-foreground" />
              </button>
            )}
            
            {loading ? (
              <Icon name="loader" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform text-muted-foreground ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-error">{error}</p>}

        {/* Helper text */}
        {helperText && !error && <p className="mt-1 text-sm text-muted">{helperText}</p>}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            ref={dropdownRef}
            className="fixed bg-card border border-border rounded-md shadow-lg animate-fade-in z-50"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              zIndex: Z_INDEX.dropdown,
            }}
          >
            <ScrollArea
              height="auto"
              maxHeight="240px"
              autoHide={true}
              invisible={false}
              direction="vertical"
            >
              <div role="listbox">
                {filteredOptions.length === 0 ? (
                  <div className="px-4 py-2 text-sm text-muted-foreground">
                    {t('common.noResults', 'Không tìm thấy kết quả')}
                  </div>
                ) : (
                  filteredOptions.map((option, index) => (
                    <div
                      key={`option-${option.value}`}
                      role="option"
                      aria-selected={option.value === value}
                      className={`
                        px-4 py-2 cursor-pointer transition-colors
                        ${option.disabled ? 'opacity-50 cursor-not-allowed' : ''}
                        ${option.value === value ? 'bg-primary/10 text-primary' : ''}
                        ${index === highlightedIndex ? 'bg-muted' : ''}
                        ${!option.disabled && index !== highlightedIndex && option.value !== value ? 'hover:bg-muted/50' : ''}
                      `}
                      onClick={() => !option.disabled && handleOptionSelect(option.value)}
                    >
                      <div className="flex items-center space-x-2">
                        {option.icon && <span className="flex-shrink-0">{option.icon}</span>}
                        {option.imageUrl && (
                          <img 
                            src={option.imageUrl} 
                            alt={option.label}
                            className="w-5 h-5 rounded flex-shrink-0"
                          />
                        )}
                        <span className="truncate">{option.label}</span>
                        {option.value === value && (
                          <Icon name="check" size="sm" className="ml-auto text-primary" />
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

SearchableSelect.displayName = 'SearchableSelect';

export default SearchableSelect;
