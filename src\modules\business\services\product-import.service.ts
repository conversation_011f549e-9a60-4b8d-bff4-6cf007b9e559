import { apiClient } from '@/shared/api/axios';
import {
  ProductImportUploadResponse,
  ProductImportValidateRequest,
  ProductImportValidateResponse,
  ProductImportStartRequest,
  ProductImportStartResponse,
  ProductImportStatusResponse,
  ProductImportUrlData,
  ProductExcelData,
  BatchCreateProductDto,
  BatchCreateProductResponse,
} from '../types/product-import.types';

// API Endpoints
const ENDPOINTS = {
  UPLOAD: '/api/v1/business/products/import/upload',
  UPLOAD_URL: '/api/v1/business/products/import/upload-url',
  VALIDATE: '/api/v1/business/products/import/validate',
  START: '/api/v1/business/products/import/start',
  PROGRESS: '/api/v1/business/products/import/progress',
  CANCEL: '/api/v1/business/products/import/cancel',
  TEMPLATES: '/api/v1/business/products/import/templates',
  BATCH_CREATE: '/user/products/batch',
};

/**
 * Upload product file for import
 */
export const uploadProductFile = async (
  file: File,
  hasHeader: boolean = true
): Promise<ProductImportUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('hasHeader', hasHeader.toString());

  const response = await apiClient.post<ProductImportUploadResponse>(ENDPOINTS.UPLOAD, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.result;
};

/**
 * Upload product file from URL
 */
export const uploadProductFileFromUrl = async (
  urlData: ProductImportUrlData
): Promise<ProductImportUploadResponse> => {
  const response = await apiClient.post<ProductImportUploadResponse>(ENDPOINTS.UPLOAD_URL, urlData);

  return response.result;
};

/**
 * Validate product import data
 */
export const validateProductImport = async (
  request: ProductImportValidateRequest
): Promise<ProductImportValidateResponse> => {
  const response = await apiClient.post<ProductImportValidateResponse>(ENDPOINTS.VALIDATE, request);

  return response.result;
};

/**
 * Start product import process
 */
export const startProductImport = async (
  request: ProductImportStartRequest
): Promise<ProductImportStartResponse> => {
  const response = await apiClient.post<ProductImportStartResponse>(ENDPOINTS.START, request);

  return response.result;
};

/**
 * Get product import progress
 */
export const getProductImportProgress = async (
  jobId: string
): Promise<ProductImportStatusResponse> => {
  const response = await apiClient.get<ProductImportStatusResponse>(
    `${ENDPOINTS.PROGRESS}/${jobId}`
  );

  return response.result;
};

/**
 * Cancel product import job
 */
export const cancelProductImport = async (jobId: string): Promise<void> => {
  await apiClient.post(`${ENDPOINTS.CANCEL}/${jobId}`);
};

/**
 * Get product import templates
 */
export const getProductImportTemplates = async (): Promise<ProductExcelData[]> => {
  const response = await apiClient.get<ProductExcelData[]>(ENDPOINTS.TEMPLATES);
  return response.result;
};

/**
 * Batch create products
 */
export const batchCreateProducts = async (
  request: BatchCreateProductDto
): Promise<BatchCreateProductResponse> => {
  const response = await apiClient.post<BatchCreateProductResponse>(
    ENDPOINTS.BATCH_CREATE,
    request
  );

  return response.result;
};

/**
 * Download product import template
 */
export const downloadProductImportTemplate = async (): Promise<Blob> => {
  const response = await apiClient.get<Blob>(`${ENDPOINTS.TEMPLATES}/download`, {
    responseType: 'blob',
  });

  return response.result as Blob;
};

// Business Logic Services

/**
 * Validate product import data with business rules
 */
export const validateProductImportWithBusinessLogic = async (
  request: ProductImportValidateRequest
): Promise<ProductImportValidateResponse> => {
  // Add any business logic validation here before calling API

  // Validate mappings
  if (!request.mappings || request.mappings.length === 0) {
    throw new Error('Cần có ít nhất một mapping cột');
  }

  // Check required fields are mapped
  const requiredFields = ['name', 'sku', 'price'];
  const mappedFields = request.mappings.map(m => m.productField);

  for (const field of requiredFields) {
    if (!mappedFields.includes(field)) {
      throw new Error(`Trường bắt buộc "${field}" chưa được ánh xạ`);
    }
  }

  return validateProductImport(request);
};

/**
 * Start product import with business logic
 */
export const startProductImportWithBusinessLogic = async (
  request: ProductImportStartRequest
): Promise<ProductImportStartResponse> => {
  // Add any business logic here before starting import

  // Validate request
  if (!request.jobId) {
    throw new Error('Job ID là bắt buộc');
  }

  if (!request.mappings || request.mappings.length === 0) {
    throw new Error('Cần có ít nhất một mapping cột');
  }

  return startProductImport(request);
};

/**
 * Batch create products with business logic
 */
export const batchCreateProductsWithBusinessLogic = async (
  request: BatchCreateProductDto
): Promise<BatchCreateProductResponse> => {
  // Validate request
  if (!request.products || request.products.length === 0) {
    throw new Error('Cần có ít nhất một sản phẩm để tạo');
  }

  if (request.products.length > 100) {
    throw new Error('Không thể tạo quá 100 sản phẩm cùng lúc');
  }

  // Validate each product
  request.products.forEach((product, index) => {
    if (!product.name || product.name.trim().length === 0) {
      throw new Error(`Sản phẩm thứ ${index + 1}: Tên sản phẩm là bắt buộc`);
    }

    if (!product.typePrice) {
      throw new Error(`Sản phẩm thứ ${index + 1}: Loại giá là bắt buộc`);
    }

    if (product.typePrice === 'HAS_PRICE' && !product.price) {
      throw new Error(
        `Sản phẩm thứ ${index + 1}: Giá sản phẩm là bắt buộc khi loại giá là HAS_PRICE`
      );
    }
  });

  return batchCreateProducts(request);
};

/**
 * Get product import progress with enhanced error handling
 */
export const getProductImportProgressWithBusinessLogic = async (
  jobId: string
): Promise<ProductImportStatusResponse> => {
  if (!jobId) {
    throw new Error('Job ID là bắt buộc');
  }

  try {
    return await getProductImportProgress(jobId);
  } catch (error) {
    // Handle specific error cases
    if (error instanceof Error && error.message.includes('404')) {
      throw new Error('Không tìm thấy job import');
    }
    throw error;
  }
};
