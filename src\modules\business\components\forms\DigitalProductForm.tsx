import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,

} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  CreateProductResponse,
  ProductDto,
  DigitalProductConfig,
  DigitalProductVersion,
  ProductTypeEnum,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

interface DigitalProductFormProps {
  onSubmit: (
    values: CreateProductDto
  ) => Promise<
    CreateProductResponse | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse
  >;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Extended FileWithMetadata interface for version images
interface ExtendedFileWithMetadata extends FileWithMetadata {
  url?: string | undefined;
  name?: string | undefined;
  size?: number | undefined;
  type?: string | undefined;
}

// Interface cho phiên bản sản phẩm số trong form (tương tự FormEventTicketType)
interface FormDigitalProductVersion extends Partial<DigitalProductVersion> {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  description?: string;
  quantity?: number;
  sku?: string;
  minQuantityPerPurchase?: number;
  maxQuantityPerPurchase?: number;
  // Bỏ status và thêm images và customFields
  images?: ExtendedFileWithMetadata[]; // Nhiều ảnh cho phiên bản
  customFields: SelectedCustomField[];
}

// Interface cho form values
interface DigitalProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string;
  salePrice?: string;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Digital product specific fields (theo API structure)
  deliveryMethod: DigitalProductConfig['digitalFulfillmentFlow']['deliveryMethod'];
  deliveryTiming: DigitalProductConfig['digitalFulfillmentFlow']['deliveryTiming'];
  digitalProductType: DigitalProductConfig['digitalOutput']['outputType'];
  accessLink?: string;
  usageInstructions?: string;
  // Thêm trường phiên bản
  versions: FormDigitalProductVersion[];
}

/**
 * Form tạo sản phẩm số
 */
const DigitalProductForm: React.FC<DigitalProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation cho sản phẩm số
  const digitalProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Digital product specific validations (theo API structure)
      deliveryMethod: z.enum(['email', 'dashboard_download', 'sms', 'direct_message', 'zalo', 'course_activation']),
      deliveryTiming: z.enum(['immediate', 'delayed']),
      digitalProductType: z.enum(['online_course', 'file_download', 'license_key', 'ebook']),
      accessLink: z.string().optional(),
      usageInstructions: z.string().optional(),
      // Validation cho phiên bản
      versions: z.array(z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Tên phiên bản không được để trống'),
        price: z.number().min(0, 'Giá phiên bản phải >= 0'),
        currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
        description: z.string().optional(),
        quantity: z.number().min(1, 'Số lượng phải >= 1').optional(),
        sku: z.string().optional(),
        minQuantityPerPurchase: z.number().min(1, 'Số lượng tối thiểu phải >= 1').optional(),
        maxQuantityPerPurchase: z.number().min(1, 'Số lượng tối đa phải >= 1').optional(),
      })).optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho phiên bản sản phẩm số
  const [versions, setVersions] = useState<FormDigitalProductVersion[]>([
    {
      id: `version-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      description: '',
      quantity: 1,
      minQuantityPerPurchase: 1,
      maxQuantityPerPurchase: 10,
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [], // Khởi tạo mảng trường tùy chỉnh rỗng
    }
  ]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    if (!values['name'] || !values['typePrice']) {
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as DigitalProductFormValues;
      setIsUploading(true);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo advancedInfo theo API structure mẫu cho DIGITAL product
      const advancedInfo = {
        purchaseCount: 0,
        digitalFulfillmentFlow: {
          deliveryMethod: formValues.deliveryMethod || 'dashboard_download',
          deliveryTiming: formValues.deliveryTiming || 'immediate',
          deliveryDelayMinutes: 0,
          accessStatus: 'pending' as const,
        },
        digitalOutput: {
          outputType: formValues.digitalProductType || 'online_course',
          accessLink: formValues.accessLink || 'https://course.example.com/activate?token=abc123',
          loginInfo: {
            username: 'auto_generated',
            password: 'temp_password'
          },
          usageInstructions: formValues.usageInstructions || 'Vui lòng đăng nhập bằng thông tin được cung cấp để truy cập khóa học',
        },
        // Thêm variantMetadata với variants theo cấu trúc mẫu
        variantMetadata: versions.length > 0 ? {
          variants: versions.map(version => ({
            name: version.name,
            sku: version.sku || `${version.name.toUpperCase().replace(/\s+/g, '-')}-001`,
            availableQuantity: version.quantity || 1,
            minQuantityPerPurchase: version.minQuantityPerPurchase || 1,
            maxQuantityPerPurchase: version.maxQuantityPerPurchase || 1,
            price: {
              listPrice: version.price || 0,
              salePrice: Math.round((version.price || 0) * 0.8), // Giảm 20% làm sale price
              currency: version.currency || 'VND',
            },
            imagesMediaTypes: version.images && version.images.length > 0
              ? version.images.map(img => img.file?.type || 'image/jpeg')
              : ['image/jpeg'],
            description: version.description || `Phiên bản ${version.name}`,
          }))
        } : {
          variants: [
            {
              name: 'Basic',
              sku: 'BASIC-001',
              availableQuantity: 1,
              minQuantityPerPurchase: 1,
              maxQuantityPerPurchase: 1,
              price: {
                listPrice: typeof priceData === 'object' && priceData && 'listPrice' in priceData ? priceData.listPrice : 500000,
                salePrice: typeof priceData === 'object' && priceData && 'salePrice' in priceData ? priceData.salePrice : 400000,
                currency: typeof priceData === 'object' && priceData && 'currency' in priceData ? priceData.currency : 'VND',
              },
              imagesMediaTypes: ['image/jpeg'],
              description: 'Phiên bản cơ bản',
            }
          ]
        },
      };

      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.DIGITAL,
        typePrice: formValues.typePrice,
        price: priceData,
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (formValues.tags && formValues.tags.length > 0) {
        productData.tags = formValues.tags;
      }

      if (mediaFiles.length > 0) {
        productData.imagesMediaTypes = mediaFiles.map(file => file.file.type);
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Lọc ra những field có giá trị không rỗng
          const fieldValue = field.value?.['value'];
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: field.value,
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      productData.advancedInfo = advancedInfo;

      // Thêm classifications cho versions nếu có
      if (versions.length > 0) {
        productData.classifications = versions.map(version => ({
          type: version.name,
          price: {
            listPrice: version.price || 0,
            salePrice: Math.round((version.price || 0) * 0.8), // Giảm 20%
            currency: version.currency || 'VND',
          },
          customFields: version.customFields
            .filter(field => {
              const fieldValue = field.value?.['value'];
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: field.value,
            })),
          // Thêm thông tin về ảnh của version
          imagesMediaTypes: version.images && version.images.length > 0
            ? version.images.map(img => img.file?.type || 'image/jpeg')
            : [],
        }));
      }

      console.log('🔍 Final product data with classifications:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // Upload ảnh cho versions (variants) nếu có
      const responseWithClassifications = response as unknown as Record<string, unknown>;
      console.log('🔍 Full response structure:', JSON.stringify(response, null, 2));
      console.log('🔍 Has classifications:', !!responseWithClassifications?.['classifications']);
      console.log('🔍 Classifications count:', (responseWithClassifications?.['classifications'] as unknown[])?.length || 0);

      if (responseWithClassifications && responseWithClassifications['classifications'] && Array.isArray(responseWithClassifications['classifications'])) {
        try {
          // Lọc các versions có ảnh cần upload
          const versionsWithImages = versions.filter(version =>
            version.images && version.images.length > 0
          );

          if (versionsWithImages.length > 0) {
            console.log('🔍 Uploading images for versions:', versionsWithImages.length);
            console.log('🔍 Versions with images:', versionsWithImages.map(v => ({ name: v.name, imageCount: v.images?.length || 0 })));
            console.log('🔍 Response classifications:', responseWithClassifications['classifications']);

            // Upload ảnh cho từng version
            for (let versionIndex = 0; versionIndex < versionsWithImages.length; versionIndex++) {
              const version = versionsWithImages[versionIndex];

              // Kiểm tra version và images tồn tại
              if (!version || !version.images || version.images.length === 0) {
                continue;
              }

              // Tìm classification tương ứng theo index hoặc theo tên
              // Vì backend tạo classifications theo thứ tự của versions
              const classifications = responseWithClassifications['classifications'] as Record<string, unknown>[];
              const classification = classifications[versionIndex] ||
                classifications.find((c: Record<string, unknown>) =>
                  c['type'] === version.name && c['uploadUrls'] && (c['uploadUrls'] as Record<string, unknown>)['classificationId']
                );

              console.log(`🔍 Version ${versionIndex} (${version.name}):`, {
                classification: classification ? { type: classification['type'], hasUploadUrls: !!classification['uploadUrls'] } : null,
                versionImages: version.images?.length || 0
              });

              if (classification && classification['uploadUrls'] && (classification['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls']) {
                const uploadUrls = (classification['uploadUrls'] as Record<string, unknown>)['imagesUploadUrls'] as unknown[];

                console.log(`🔍 Upload URLs for version ${version.name}:`, uploadUrls.length);

                if (uploadUrls.length > 0 && version.images.length > 0) {
                  // Upload từng ảnh của version
                  const imagesToUpload = version.images.slice(0, uploadUrls.length);

                  // Tạo mapping giữa image files và upload URLs từ backend
                  const uploadTasks = imagesToUpload.slice(0, uploadUrls.length).map((fileData, index) => {
                    const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                    if (!uploadInfo) {
                      throw new Error(`Upload info not found for index ${index}`);
                    }
                    return {
                      file: fileData.file,
                      uploadUrl: uploadInfo['url'] as string,
                      key: uploadInfo['key'] as string,
                      index: uploadInfo['index'] as number,
                    };
                  });

                  // Upload tất cả ảnh của version cùng lúc với Promise.all
                  const filesToUpload = uploadTasks.map((task, index) => ({
                    file: task.file,
                    id: `${Date.now()}_${version.id}_${index}`,
                  }));
                  const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

                  // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
                  await uploadProductImages(filesToUpload, urlsToUpload, {
                    skipCacheInvalidation: true,
                  });

                  console.log(`✅ Uploaded ${imagesToUpload.length} images for version ${version.name}`);
                }
              }
            }

            NotificationUtil.success({
              message: t('business:product.form.validation.versionImagesUploadSuccess', 'Tải lên ảnh phiên bản thành công'),
              duration: 3000,
            });
          }
        } catch (versionUploadError) {
          console.error('❌ Error uploading version images:', versionUploadError);
          NotificationUtil.warning({
            message: t('business:product.form.validation.versionImagesUploadError', 'Có lỗi xảy ra khi tải lên ảnh phiên bản'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in DigitalProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: DigitalProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }
      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
          type: (fieldData?.['type'] as string) || 'text',
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: '' },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phiên bản mới
  const handleAddVersion = useCallback(() => {
    const newVersion: FormDigitalProductVersion = {
      id: `version-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      description: '',
      quantity: 1,
      minQuantityPerPurchase: 1,
      maxQuantityPerPurchase: 10,
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [], // Khởi tạo mảng trường tùy chỉnh rỗng
    };
    setVersions(prev => [...prev, newVersion]);
  }, []);

  // Xóa phiên bản
  const handleRemoveVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.filter(version => version.id !== versionId));
  }, []);

  // Cập nhật thông tin phiên bản
  const handleUpdateVersion = useCallback((versionId: string, field: keyof FormDigitalProductVersion, value: string | number) => {
    setVersions(prev => prev.map(version => {
      if (version.id === versionId) {
        return {
          ...version,
          [field]: value
        };
      }
      return version;
    }));
  }, []);

  // Xử lý upload nhiều ảnh cho phiên bản
  const handleVersionImagesChange = useCallback(
    (versionId: string, files: ExtendedFileWithMetadata[]) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return { ...version, images: files };
          }
          return version;
        })
      );
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phiên bản
  const handleToggleCustomFieldToVersion = useCallback(
    (versionId: string, fieldId: number, fieldData?: Record<string, unknown>) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            // Kiểm tra xem trường đã tồn tại trong phiên bản chưa
            const existingFieldIndex = version.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Nếu đã tồn tại, xóa nó (bỏ chọn)
              return {
                ...version,
                customFields: version.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Thêm trường mới vào phiên bản với thông tin đầy đủ
            return {
              ...version,
              customFields: [
                ...version.customFields,
                {
                  id: Date.now(), // ID tạm thời
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component:
                    (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
                  type: (fieldData?.['type'] as string) || 'text',
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: '' }, // Giá trị mặc định
                },
              ],
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phiên bản
  const handleRemoveCustomFieldFromVersion = useCallback(
    (versionId: string, customFieldId: number) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phiên bản
  const handleUpdateCustomFieldInVersion = useCallback(
    (versionId: string, customFieldId: number, value: string) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Digital product defaults (theo API structure)
      deliveryMethod: 'dashboard_download' as const,
      deliveryTiming: 'immediate' as const,
      digitalProductType: 'file_download' as const,
      accessLink: '',
      usageInstructions: '',
      // Phiên bản mặc định
      versions: [],
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createDigitalTitle', 'Tạo sản phẩm số')}>
      <Form
        ref={formRef}
        schema={digitalProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.namePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.descriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Giá sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '2. Giá sản phẩm')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                ]}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem name="listPrice" label={t('business:product.listPrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.enterListPrice')} />
                </FormItem>
                <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                  <Input fullWidth type="number" min="0" placeholder={t('business:product.enterSalePrice')} />
                </FormItem>
                <FormItem name="currency" label={t('business:product.currency')} required>
                  <Controller
                    name="currency"
                    render={({ field }) => (
                      <Select
                        fullWidth
                        value={field.value || 'VND'}
                        onChange={value => field.onChange(value)}
                        options={[
                          { value: 'VND', label: 'VND' },
                          { value: 'USD', label: 'USD' },
                          { value: 'EUR', label: 'EUR' },
                        ]}
                      />
                    )}
                  />
                </FormItem>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder={t('business:product.form.priceDescriptionPlaceholder')}
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>
        {/* 5. Hình ảnh sản phẩm */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '5. Hình ảnh sản phẩm')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>
        {/* 3. Quy trình xử lý đơn hàng số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalProcessing', '3. Quy trình xử lý đơn hàng số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="deliveryMethod" label={t('business:product.form.digitalProduct.deliveryMethod.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'email', label: t('business:product.form.digitalProduct.deliveryMethod.email') },
                  { value: 'dashboard_download', label: t('business:product.form.digitalProduct.deliveryMethod.dashboardDownload') },
                  { value: 'sms', label: t('business:product.form.digitalProduct.deliveryMethod.sms') },
                  { value: 'direct_message', label: t('business:product.form.digitalProduct.deliveryMethod.directMessage') },
                  { value: 'zalo', label: t('business:product.form.digitalProduct.deliveryMethod.zalo') },
                  { value: 'course_activation', label: t('business:product.form.digitalProduct.deliveryMethod.courseActivation') },
                ]}
              />
            </FormItem>

            <FormItem name="deliveryTiming" label={t('business:product.form.digitalProduct.deliveryTiming.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'immediate', label: t('business:product.form.digitalProduct.deliveryTiming.immediate') },
                  { value: 'delayed', label: t('business:product.form.digitalProduct.deliveryTiming.delayed') },
                ]}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 4. Đầu ra sản phẩm số */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.digitalOutput', '4. Đầu ra sản phẩm số')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="digitalProductType" label={t('business:product.form.digitalProduct.digitalProductType.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'online_course', label: t('business:product.form.digitalProduct.digitalProductType.onlineCourse') },
                  { value: 'file_download', label: t('business:product.form.digitalProduct.digitalProductType.fileDownload') },
                  { value: 'license_key', label: t('business:product.form.digitalProduct.digitalProductType.licenseKey') },
                  { value: 'ebook', label: t('business:product.form.digitalProduct.digitalProductType.ebook') },
                ]}
              />
            </FormItem>

            <FormItem name="accessLink" label={t('business:product.form.digitalProduct.accessLink')}>
              <Input fullWidth placeholder={t('business:product.form.digitalProduct.accessLinkPlaceholder')} />
            </FormItem>

            <FormItem name="usageInstructions" label={t('business:product.form.digitalProduct.usageInstructions')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.digitalProduct.usageInstructionsPlaceholder')}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 6. Phiên bản */}
        <CollapsibleCard
          title={
            <div className="flex items-center justify-between w-full">
              <Typography variant="h6" className="font-medium">
                {versions.length > 0
                  ? `6. ${t('business:product.form.versions.title', 'Phiên bản')} (${versions.length})`
                  : `6. ${t('business:product.form.versions.title', 'Phiên bản')}`
                }
              </Typography>
              <div
                onClick={e => {
                  e.stopPropagation();
                  handleAddVersion();
                }}
                className="cursor-pointer"
              >
                <IconCard
                  icon="plus"
                  title={t('business:product.form.versions.addVersion', 'Thêm phiên bản')}
                  variant="primary"
                  size="sm"
                />
              </div>
            </div>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {versions.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <Typography variant="body2">
                  {t('business:product.form.versions.noVersions', 'Chưa có phiên bản nào. Nhấn "Thêm phiên bản" để bắt đầu.')}
                </Typography>
              </div>
            ) : (
              <div className="space-y-4">
                {versions.map((version, index) => (
                  <CollapsibleCard
                    key={version.id}
                    title={
                      <div className="flex justify-between items-center w-full">
                        <div className="flex items-center space-x-4">
                          <Typography variant="body2" className="font-medium">
                            {version.name || `Phiên bản ${index + 1}`}
                          </Typography>
                          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                            {version.price > 0 ? `${version.price.toLocaleString()} ${version.currency}` : '0 VND'}
                          </Typography>
                          <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                            {version.quantity} bản
                          </Typography>
                          {version.id && (
                            <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                              ID: {version.id.split('-').pop()}
                            </Typography>
                          )}
                        </div>
                        <div
                          onClick={e => {
                            e.stopPropagation();
                            handleRemoveVersion(version.id!);
                          }}
                          className="cursor-pointer"
                        >
                          <IconCard
                            icon="trash"
                            title={t('business:product.form.versions.removeVersion', 'Xóa phiên bản')}
                            variant="danger"
                            size="sm"
                          />
                        </div>
                      </div>
                    }
                    defaultOpen={true}
                  >
                    <div className="space-y-4">
                      {/* Tên phiên bản và giá */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <FormItem label={t('business:product.form.versions.name', 'Tên phiên bản')} required>
                          <Input
                            fullWidth
                            placeholder={t('business:product.form.versions.namePlaceholder', 'Basic, Pro, Premium...')}
                            value={version.name}
                            onChange={(e) => handleUpdateVersion(version.id!, 'name', e.target.value)}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.price', 'Giá')} required>
                          <Input
                            fullWidth
                            type="number"
                            min="0"
                            placeholder="0"
                            value={version.price}
                            onChange={(e) => handleUpdateVersion(version.id!, 'price', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.currency', 'Đơn vị tiền tệ')} required>
                          <Select
                            fullWidth
                            value={version.currency || 'VND'}
                            onChange={(value) => {
                              const selectedValue = Array.isArray(value) ? value[0] : value;
                              if (selectedValue !== undefined) {
                                handleUpdateVersion(version.id!, 'currency', selectedValue);
                              }
                            }}
                            options={[
                              { value: 'VND', label: 'VND' },
                              { value: 'USD', label: 'USD' },
                              { value: 'EUR', label: 'EUR' },
                            ]}
                          />
                        </FormItem>
                      </div>

                      {/* Mô tả phiên bản */}
                      <FormItem label={t('business:product.form.versions.description', 'Mô tả phiên bản')}>
                        <Textarea
                          fullWidth
                          rows={3}
                          placeholder={t('business:product.form.versions.descriptionPlaceholder', 'Mô tả chi tiết về phiên bản này...')}
                          value={version.description || ''}
                          onChange={(e) => handleUpdateVersion(version.id!, 'description', e.target.value)}
                        />
                      </FormItem>

                      {/* Số lượng và SKU */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem label={t('business:product.form.versions.quantity', 'Số lượng có sẵn')} required>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="100"
                            value={version.quantity}
                            onChange={(e) => handleUpdateVersion(version.id!, 'quantity', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.sku', 'Mã SKU')}>
                          <Input
                            fullWidth
                            placeholder={t('business:product.form.versions.skuPlaceholder', 'BASIC-001')}
                            value={version.sku || ''}
                            onChange={(e) => handleUpdateVersion(version.id!, 'sku', e.target.value)}
                          />
                        </FormItem>
                      </div>

                      {/* Số lượng mua tối thiểu và tối đa */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItem label={t('business:product.form.versions.minQuantity', 'Số lượng tối thiểu mỗi lần mua')}>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="1"
                            value={version.minQuantityPerPurchase}
                            onChange={(e) => handleUpdateVersion(version.id!, 'minQuantityPerPurchase', Number(e.target.value))}
                          />
                        </FormItem>
                        <FormItem label={t('business:product.form.versions.maxQuantity', 'Số lượng tối đa mỗi lần mua')}>
                          <Input
                            fullWidth
                            type="number"
                            min="1"
                            placeholder="10"
                            value={version.maxQuantityPerPurchase}
                            onChange={(e) => handleUpdateVersion(version.id!, 'maxQuantityPerPurchase', Number(e.target.value))}
                          />
                        </FormItem>
                      </div>

                      {/* Hình ảnh phiên bản */}
                      <FormItem label={t('business:product.form.versions.images', 'Ảnh phiên bản')}>
                        <MultiFileUpload
                          value={version.images || []}
                          onChange={(files: FileWithMetadata[]) => {
                            // Convert FileWithMetadata to ExtendedFileWithMetadata
                            const extendedFiles: ExtendedFileWithMetadata[] = files.map(file => ({
                              ...file,
                              url: file.preview || undefined,
                              name: file.file.name || undefined,
                              size: file.file.size || undefined,
                              type: file.file.type || undefined,
                            }));
                            handleVersionImagesChange(version.id!, extendedFiles);
                          }}
                          accept="image/*"
                          mediaOnly={true}
                          placeholder={t(
                            'business:product.form.versions.imagesPlaceholder',
                            'Chọn ảnh cho phiên bản này'
                          )}
                          className="w-full"
                        />
                      </FormItem>

                      {/* Trường tùy chỉnh cho phiên bản */}
                      <div className="space-y-4">
                        <Typography variant="body2" className="font-medium">
                          {t('business:product.form.versions.customFields', 'Thuộc tính phiên bản')}
                        </Typography>

                        <SimpleCustomFieldSelector
                          onFieldSelect={fieldData => {
                            handleToggleCustomFieldToVersion(
                              version.id!,
                              fieldData.id,
                              fieldData as unknown as Record<string, unknown>
                            );
                          }}
                          selectedFieldIds={version.customFields.map(f => f.fieldId)}
                          placeholder={t(
                            'business:product.form.versions.searchCustomField',
                            'Nhập từ khóa và nhấn Enter để tìm thuộc tính...'
                          )}
                        />

                        {version.customFields.length > 0 && (
                          <div className="space-y-3">
                            {version.customFields.map(field => (
                              <CustomFieldRenderer
                                key={field.id}
                                field={field}
                                value={(field.value?.['value'] as string) || ''}
                                onChange={value =>
                                  handleUpdateCustomFieldInVersion(
                                    version.id!,
                                    field.id,
                                    value as string
                                  )
                                }
                                onRemove={() =>
                                  handleRemoveCustomFieldFromVersion(version.id!, field.id)
                                }
                              />
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CollapsibleCard>
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '7. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(f => f.fieldId)}
              placeholder={t(
                'business:product.form.customFields.searchPlaceholder',
                'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
              )}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-3">
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={(field.value?.['value'] as string) || ''}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              isSubmitting || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo sản phẩm')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={isSubmitting || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default DigitalProductForm;
