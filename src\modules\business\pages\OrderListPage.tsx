import React, { useMemo, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  StatusBadge,
  Button,
  SlideInForm,
  ActionMenu,
} from '@/shared/components/common';
import type { ActionMenuItem } from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useSlideForm } from '@/shared/hooks/useSlideForm';
import { useOrders, useCreateOrder } from '../hooks/useOrderQuery';
import { OrderStatus, OrderQueryParams, PaymentMethod, CreateOrderData } from '../services/order.service';
import { ShippingStatus, CreateEnhancedOrderDto } from '../types/order.types';
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';
import EnhancedOrderForm from '../components/forms/EnhancedOrderForm';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang danh sách đơn hàng
 */
const OrderListPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);
  const navigate = useNavigate();
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Hook quản lý SlideInForm
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Hook tạo đơn hàng
  const createOrderMutation = useCreateOrder();

  // Hook cập nhật đơn hàng
  // const updateOrderMutation = useUpdateOrder();

  // Xử lý xem chi tiết đơn hàng
  const handleViewOrder = useCallback((orderId: string) => {
    navigate(`/business/order/${orderId}`);
  }, [navigate]);

  // Xử lý chỉnh sửa đơn hàng
  const handleEditOrder = useCallback((orderId: string) => {
    navigate(`/business/order/${orderId}/edit`);
  }, [navigate]);

  // Xử lý in đơn hàng
  const handlePrintOrder = useCallback((orderId: string) => {
    // TODO: Implement print functionality
    console.log('Print order:', orderId);
    NotificationUtil.info({
      title: t('business:order.print'),
      message: t('business:order.printInProgress', 'Đang chuẩn bị in đơn hàng...'),
      duration: 3000,
    });
  }, [t]);

  // Cấu hình columns cho table
  const columns = useMemo(() => [
    {
      title: t('business:order.orderNumber'),
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      sortable: true,
      render: (value: unknown, record: { id: string }) => {
        const orderNumber = value as string;
        return (
          <Button
            variant="ghost"
            onClick={() => navigate(`/business/order/${record.id}`)}
            className="p-0 h-auto font-medium text-primary hover:text-primary-dark"
          >
            {orderNumber}
          </Button>
        );
      },
    },
    {
      title: t('business:order.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      sortable: true,
    },
    {
      title: t('business:order.totalAmount'),
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      sortable: true,
      render: (value: unknown) => formatCurrency(value as number),
    },
    {
      title: t('business:order.status.title'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => {
        const orderStatus = status as OrderStatus;
        const statusConfig = {
          [OrderStatus.PENDING]: { variant: 'warning', label: t('business:order.status.pending') },
          [OrderStatus.PROCESSING]: { variant: 'info', label: t('business:order.status.processing') },
          [OrderStatus.COMPLETED]: { variant: 'success', label: t('business:order.status.completed') },
          [OrderStatus.CANCELLED]: { variant: 'danger', label: t('business:order.status.cancelled') },
          [OrderStatus.REFUNDED]: { variant: 'info', label: t('business:order.status.refunded') },
        };
        const config = statusConfig[orderStatus] || { variant: 'info', label: orderStatus };
        return <StatusBadge text={config.label} variant={config.variant as 'warning' | 'info' | 'success' | 'danger'} />;
      },
    },
    {
      title: t('business:order.paymentStatus.title'),
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (status: unknown) => {
        const paymentStatus = status as string;
        const statusConfig = {
          'PAID': { variant: 'success', label: t('business:order.paymentStatus.paid') },
          'UNPAID': { variant: 'danger', label: t('business:order.paymentStatus.unpaid') },
          'PARTIALLY_PAID': { variant: 'warning', label: t('business:order.paymentStatus.partiallyPaid') },
        };
        const config = statusConfig[paymentStatus as keyof typeof statusConfig] || { variant: 'info', label: paymentStatus };
        return <StatusBadge text={config.label} variant={config.variant as 'warning' | 'info' | 'success' | 'danger'} />;
      },
    },
    {
      title: t('business:common.shippingStatus.title', 'Trạng thái vận chuyển'),
      dataIndex: 'shippingStatus',
      key: 'shippingStatus',
      render: (status: unknown) => {
        const shippingStatus = status as ShippingStatus;
        const statusConfig = {
          [ShippingStatus.PENDING]: { variant: 'warning', label: t('business:common.shippingStatus.pending') },
          [ShippingStatus.PICKED_UP]: { variant: 'info', label: t('business:common.shippingStatus.pickedUp') },
          [ShippingStatus.IN_TRANSIT]: { variant: 'info', label: t('business:common.shippingStatus.inTransit') },
          [ShippingStatus.DELIVERED]: { variant: 'success', label: t('business:common.shippingStatus.delivered') },
          [ShippingStatus.FAILED]: { variant: 'danger', label: t('business:common.shippingStatus.failed') },
          [ShippingStatus.RETURNED]: { variant: 'info', label: t('business:common.shippingStatus.returned') },
        };
        const config = statusConfig[shippingStatus] || { variant: 'info', label: shippingStatus };
        return <StatusBadge text={config.label} variant={config.variant as 'warning' | 'info' | 'success' | 'danger'} />;
      },
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: unknown) => formatTimestamp(value as number),
    },
    {
      title: t('common:actions'),
      key: 'actions',
      width: 120,
      render: (_: unknown, record: { id: string }) => {
        // Tạo danh sách các action items
        const actionItems: ActionMenuItem[] = [
          {
            id: 'view',
            label: t('common:view'),
            icon: 'eye',
            onClick: () => handleViewOrder(record.id),
            tooltip: t('common:view'),
            showDirect: false,
          },
          {
            id: 'edit',
            label: t('common:edit'),
            icon: 'edit',
            onClick: () => handleEditOrder(record.id),
            tooltip: t('common:edit'),
            showDirect: false,
          },
          {
            id: 'print',
            label: t('business:order.print'),
            icon: 'printer',
            onClick: () => handlePrintOrder(record.id),
            tooltip: t('business:order.print'),
            showDirect: false, // Ẩn trong menu
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('common:moreActions', 'Thêm hành động')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ], [t, navigate, handleEditOrder, handlePrintOrder, handleViewOrder]);

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: 'ASC' | 'DESC' | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection;
    }

    // Thêm filter nếu có
    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = params.filterValue as OrderStatus;
    }

    // Xử lý dateRange nếu có
    if (params.dateRange[0] && params.dateRange[1]) {
      queryParams.fromDate = params.dateRange[0].toISOString().split('T')[0];
      queryParams.toDate = params.dateRange[1].toISOString().split('T')[0];
    }

    return queryParams;
  }, []);

  // Cấu hình data table
  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams,
  }));

  // Lấy dữ liệu đơn hàng
  const { data: ordersData, isLoading } = useOrders(dataTable.queryParams);

  // Xử lý tạo đơn hàng mới
  const handleCreateOrder = () => {
    showCreateForm();
  };

  // Xử lý submit form tạo đơn hàng
  const handleSubmitCreateOrder = async (orderData: CreateEnhancedOrderDto) => {
    setIsSubmitting(true);
    try {
      console.log('Creating order with data:', orderData);

      // Transform data để phù hợp với API hiện tại
      const apiOrderData: CreateOrderData = {
        customerId: orderData.customer.id || 0, // Sẽ cần tạo customer trước nếu chưa có
        items: orderData.items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          productType: item.productType,
        })),
        paymentMethod: orderData.payment.method as PaymentMethod,
      };

      // Thêm thông tin shipping nếu có
      if (orderData.shipping) {
        apiOrderData.shipping = {
          method: orderData.shipping.method,
          serviceId: orderData.shipping.serviceId,
          serviceName: orderData.shipping.serviceName,
          fromAddress: orderData.shipping.fromAddress,
          toAddress: orderData.shipping.toAddress,
          fee: orderData.shipping.fee,
          estimatedDelivery: orderData.shipping.estimatedDelivery,
          note: orderData.shipping.note,
          // Truyền addressId nếu có từ shipping data
          ...(orderData.shipping.addressId && { addressId: orderData.shipping.addressId }),
        };
      }

      // Thêm thông tin digital delivery nếu có
      if (orderData.digitalDelivery) {
        apiOrderData.digitalDelivery = orderData.digitalDelivery;
      }

      // Thêm thông tin payment nếu có
      if (orderData.payment) {
        apiOrderData.payment = {
          method: orderData.payment.method,
          status: orderData.payment.status,
          codAmount: orderData.payment.codAmount,
        };
      }

      // Chỉ thêm notes nếu có giá trị
      if (orderData.notes) {
        apiOrderData.notes = orderData.notes;
      }

      // Thêm tags nếu có
      if (orderData.tags && orderData.tags.length > 0) {
        apiOrderData.tags = orderData.tags;
      }

      console.log('🔍 [OrderListPage] Transformed API order data:', JSON.stringify(apiOrderData, null, 2));

      // Gọi API tạo đơn hàng
      const result = await createOrderMutation.mutateAsync(apiOrderData);

      console.log('Order created successfully:', result);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        title: t('business:order.createSuccess'),
        message: t('business:order.createSuccessDescription'),
        duration: 5000,
      });

      // Đóng form
      hideCreateForm();

    } catch (error) {
      console.error('Error creating order:', error);

      // Hiển thị thông báo lỗi
      NotificationUtil.error({
        title: t('business:order.createError'),
        message: t('business:order.createErrorDescription'),
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý hủy tạo đơn hàng
  const handleCancelCreateOrder = () => {
    hideCreateForm();
  };

  // Xử lý xóa đơn hàng
  const handleDeleteOrders = () => {
    // TODO: Implement delete functionality
    console.log('Delete orders:', selectedItems);
  };

  // Sử dụng useActiveFilters hook như trong ProductsPage
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [OrderStatus.PENDING]: t('business:order.status.pending'),
      [OrderStatus.PROCESSING]: t('business:order.status.processing'),
      [OrderStatus.COMPLETED]: t('business:order.status.completed'),
      [OrderStatus.CANCELLED]: t('business:order.status.cancelled'),
      [OrderStatus.REFUNDED]: t('business:order.status.refunded'),
    },
    t,
  });

  // Cấu hình MenuIconBar items
  const menuItems = [
    {
      id: 'all',
      label: t('common:all'),
      icon: 'list' as const,
      onClick: () => console.log('All orders'),
    },
    {
      id: 'pending',
      label: t('business:order.status.pending'),
      icon: 'clock' as const,
      onClick: () => console.log('Pending orders'),
    },
    {
      id: 'completed',
      label: t('business:order.status.completed'),
      icon: 'check' as const,
      onClick: () => console.log('Completed orders'),
    },
  ];

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleCreateOrder}
        items={menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleDeleteOrders,
            className: 'text-red-500',
            condition: selectedItems.length > 0,
          },
        ]}
      />

      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={() => dataTable.dateRange.setDateRange([null, null])}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho tạo đơn hàng */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <EnhancedOrderForm
          onSubmit={handleSubmitCreateOrder}
          onCancel={handleCancelCreateOrder}
          isSubmitting={isSubmitting}
          shopId={1} // TODO: Lấy shopId từ user context hoặc API
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={ordersData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys: selectedItems,
            onChange: keys => setSelectedItems(keys as number[]),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: ordersData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: ordersData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default OrderListPage;
