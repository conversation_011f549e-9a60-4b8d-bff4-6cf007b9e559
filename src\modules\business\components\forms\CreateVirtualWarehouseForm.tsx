import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Card,
  Typography,
  Select,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

import { CreateVirtualWarehouseFormValues } from '../../types/virtual-warehouse.types';
import { useCreateVirtualWarehouse } from '../../hooks/useVirtualWarehouseQuery';
import { useWarehouses } from '../../hooks/useWarehouseQuery';

interface CreateVirtualWarehouseFormProps {
  onSubmit: (values: CreateVirtualWarehouseFormValues) => void;
  onCancel: () => void;
}

/**
 * Form tạo mới kho ảo
 */
const CreateVirtualWarehouseForm: React.FC<CreateVirtualWarehouseFormProps> = ({
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const formRef = useRef<FormRef<CreateVirtualWarehouseFormValues>>(null);

  const { mutateAsync: createVirtualWarehouse, isPending } = useCreateVirtualWarehouse();

  // Lấy danh sách warehouses để chọn
  const { data: warehousesData } = useWarehouses({
    page: 1,
    limit: 100, // Lấy nhiều để có đủ options
  });

  const handleSubmit = async (values: CreateVirtualWarehouseFormValues) => {
    try {
      if (!values.warehouseId) {
        throw new Error('Warehouse ID is required');
      }

      await createVirtualWarehouse({
        warehouseId: values.warehouseId,
        data: {
          ...(values.associatedSystem && { associatedSystem: values.associatedSystem }),
          ...(values.purpose && { purpose: values.purpose }),
        },
      });
      onSubmit(values);
    } catch (error) {
      console.error('Create virtual warehouse error:', error);
    }
  };

  // Tạo options cho warehouse select
  const warehouseOptions = warehousesData?.result?.items?.map(warehouse => ({
    value: warehouse.warehouseId,
    label: `${warehouse.name} (${warehouse.type})`,
  })) || [];

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h5" className="mb-6">
          {t('business:virtualWarehouse.form.createTitle')}
        </Typography>

        <Form
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          ref={formRef as any}
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          onSubmit={handleSubmit as any}
          className="space-y-6"
        >
          <FormItem
            name="warehouseId"
            label={t('business:warehouse.title')}
            required
          >
            <Select
              options={warehouseOptions}
              placeholder={t('business:virtualWarehouse.form.warehousePlaceholder')}
              disabled={isPending}
            />
          </FormItem>

          <FormItem
            name="associatedSystem"
            label={t('business:virtualWarehouse.associatedSystem')}
          >
            <Input
              placeholder={t('business:virtualWarehouse.form.associatedSystemPlaceholder')}
              disabled={isPending}
            />
          </FormItem>

          <FormItem
            name="purpose"
            label={t('business:virtualWarehouse.purpose')}
          >
            <Textarea
              placeholder={t('business:virtualWarehouse.form.purposePlaceholder')}
              rows={3}
              disabled={isPending}
            />
          </FormItem>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isPending}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={isPending}
            >
              {t('business:virtualWarehouse.form.create')}
            </Button>
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default CreateVirtualWarehouseForm;
export type { CreateVirtualWarehouseFormValues };
