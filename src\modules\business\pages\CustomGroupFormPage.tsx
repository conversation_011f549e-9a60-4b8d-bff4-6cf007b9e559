import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { Card, Table, ActionMenu, Loading } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import type { ActionMenuItem } from '@/shared/components/common/ActionMenu/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useFormErrors } from '@/shared/hooks/';
import CustomGroupFormForm, { CustomGroupFormValues } from '../components/forms/CustomGroupFormForm';
import {
  useCustomGroupForms,
  useCreateCustomGroupForm,
  useUpdateCustomGroupForm,
  // useDeleteCustomGroupForm, // TODO: Will be used for delete functionality
} from '../hooks/useCustomGroupForm';
import type { CustomGroupFormListItem } from '../services/custom-group-form.service';

// Enum cho trạng thái nhóm trường tùy chỉnh
enum CustomGroupFormStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

// Interface cho dữ liệu nhóm trường tùy chỉnh
interface CustomGroupForm {
  id: string;
  label: string;
  description?: string | undefined;
  status: CustomGroupFormStatus;
  fieldCount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Trang quản lý nhóm trường tùy chỉnh
 */
const CustomGroupFormPage: React.FC = () => {
  const { t } = useTranslation('business');

  // State cho filter
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State cho edit mode
  const [editingItem, setEditingItem] = useState<CustomGroupForm | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Form errors hook
  const { formRef, setFormErrors } = useFormErrors<CustomGroupFormValues>();

  // API hooks
  const {
    data: customGroupFormData,
    isLoading,
    error,
  } = useCustomGroupForms({
    page: currentPage,
    limit: pageSize,
    ...(searchTerm && { search: searchTerm }),
  });

  const createCustomGroupFormMutation = useCreateCustomGroupForm();
  const updateCustomGroupFormMutation = useUpdateCustomGroupForm();
  // const deleteCustomGroupFormMutation = useDeleteCustomGroupForm(); // TODO: Implement delete functionality

  // Transform API data for table
  const tableData = useMemo(() => {
    if (!customGroupFormData?.result?.items) return [];

    return customGroupFormData.result.items.map((item: CustomGroupFormListItem) => {
      // Safely convert timestamp to ISO string
      const getISOString = (timestamp: number | null | undefined): string => {
        if (!timestamp || typeof timestamp !== 'number' || isNaN(timestamp)) {
          return new Date().toISOString(); // Fallback to current date
        }
        const date = new Date(timestamp);
        return isNaN(date.getTime()) ? new Date().toISOString() : date.toISOString();
      };

      return {
        id: item.id.toString(),
        label: item.label,
        // ...(item.description !== undefined && { description: item.description }),  // Note: API doesn't provide description in list items
        status: CustomGroupFormStatus.ACTIVE, // Default status
        fieldCount: item.fieldCount,
        createdAt: getISOString(item.createAt),
        updatedAt: getISOString(item.createAt),
      };
    });
  }, [customGroupFormData]);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<CustomGroupForm>[] = [
    {
      key: 'label',
      title: t('customGroupForm.form.label'),
      dataIndex: 'label',
      sortable: true,
    },
    {
      key: 'fieldCount',
      title: t('customGroupForm.form.fieldCount'),
      dataIndex: 'fieldCount',
      render: (value: unknown) => {
        return `${value} ${t('customGroupForm.form.fields')}`;
      },
      sortable: true,
    },
    {
      key: 'status',
      title: t('customGroupForm.form.status'),
      dataIndex: 'status',
      render: (value: unknown) => {
        const status = value as CustomGroupFormStatus;
        const statusMap = {
          [CustomGroupFormStatus.ACTIVE]: { text: t('customGroupForm.status.active'), color: 'success' },
          [CustomGroupFormStatus.INACTIVE]: { text: t('customGroupForm.status.inactive'), color: 'danger' },
          [CustomGroupFormStatus.DRAFT]: { text: t('customGroupForm.status.draft'), color: 'secondary' },
        };

        return <span className={`text-${statusMap[status].color}`}>{statusMap[status].text}</span>;
      },
      sortable: true,
    },
    {
      key: 'createdAt',
      title: t('common.createdAt'),
      dataIndex: 'createdAt',
      render: (value: unknown) => {
        return new Date(value as string).toLocaleDateString('vi-VN');
      },
      sortable: true,
    },
    {
      key: 'actions',
      title: t('common.actions'),
      width: '15%',
      render: (_, record) => {
        const actionItems: ActionMenuItem[] = [
          {
            id: 'edit',
            label: t('common.edit'),
            icon: 'edit',
            onClick: () => handleEdit(record),
          },
          {
            id: 'delete',
            label: t('common.delete'),
            icon: 'trash',
            onClick: () => handleDelete(record),
          },
        ];

        return (
          <ActionMenu
            items={actionItems}
            menuTooltip={t('common.moreActions')}
            iconSize="sm"
            iconVariant="default"
            placement="bottom"
            menuWidth="180px"
            showAllInMenu={false}
            preferRight={true}
          />
        );
      },
    },
  ];

  // Lọc dữ liệu (client-side filtering for status since API doesn't support it yet)
  const filteredData = useMemo(() => {
    return tableData.filter((item) => {
      const matchesFilter = filter === 'all' || item.status === filter;
      return matchesFilter;
    });
  }, [tableData, filter]);

  // Xử lý thêm mới
  const handleAdd = () => {
    // Clear form errors khi mở form mới
    setFormErrors({});
    setEditingItem(null);
    setIsEditMode(false);
    showForm();
  };

  // Xử lý chỉnh sửa
  const handleEdit = (record: CustomGroupForm) => {
    setEditingItem(record);
    setIsEditMode(true);
    setFormErrors({});
    showForm();
  };

  // Xử lý xóa
  const handleDelete = (record: CustomGroupForm) => {
    console.log('Delete', record.id);
    // TODO: Implement delete functionality
  };

  // Xử lý submit form
  const handleSubmit = async (values: CustomGroupFormValues) => {
    try {
      if (isEditMode && editingItem) {
        // Update existing item
        await updateCustomGroupFormMutation.mutateAsync({
          id: parseInt(editingItem.id),
          data: {
            label: values.label,
          },
        });
      } else {
        // Create new item
        await createCustomGroupFormMutation.mutateAsync({
          label: values.label,
        });
      }
      hideForm();
      setEditingItem(null);
      setIsEditMode(false);
    } catch (error) {
      console.error('Error saving custom group form:', error);

      // Handle API errors
      if (error instanceof AxiosError && error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      }
    }
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
    setEditingItem(null);
    setIsEditMode(false);
    setFormErrors({});
  };

  // Show loading state
  if (isLoading) {
    return <Loading />;
  }

  // Show error state
  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">{t('common.error')}: {error.message}</p>
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={setSearchTerm}
        onAdd={handleAdd}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => setFilter('all'),
          },
          {
            id: 'active',
            label: t('customGroupForm.status.active'),
            icon: 'check',
            onClick: () => setFilter(CustomGroupFormStatus.ACTIVE),
          },
          {
            id: 'inactive',
            label: t('customGroupForm.status.inactive'),
            icon: 'eye-off',
            onClick: () => setFilter(CustomGroupFormStatus.INACTIVE),
          },
          {
            id: 'draft',
            label: t('customGroupForm.status.draft'),
            icon: 'file',
            onClick: () => setFilter(CustomGroupFormStatus.DRAFT),
          },
        ]}
      />

      <SlideInForm isVisible={isVisible}>
        <Card>
          <CustomGroupFormForm
            formRef={formRef}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            title={isEditMode ? t('customGroupForm.editForm') : t('customGroupForm.addForm')}
            isLoading={createCustomGroupFormMutation.isPending || updateCustomGroupFormMutation.isPending}
            initialData={editingItem ? { label: editingItem.label } : {}}
          />
        </Card>
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={filteredData}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customGroupFormData?.result?.meta?.totalItems || 0,
            showSizeChanger: true,
            showFirstLastButtons: true,
            showPageInfo: true,
            onChange: (page: number, size?: number) => {
              setCurrentPage(page);
              if (size && size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
        />
      </Card>
    </div>
  );
};

export default CustomGroupFormPage;
