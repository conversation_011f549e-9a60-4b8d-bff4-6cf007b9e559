import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Card, Input, Textarea, Checkbox, FormItem } from '@/shared/components/common';
import { PointDto } from '../../types';
import { formatCurrency } from '@/shared/utils/format';

interface PointDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pointDetail: PointDto | undefined;
  isLoading: boolean;
}

/**
 * Modal hiển thị chi tiết gói R-Point
 */
const PointDetailModal: React.FC<PointDetailModalProps> = ({
  isOpen,
  onClose,
  pointDetail,
  isLoading,
}) => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('rpointAdmin:points.detail.title', 'Chi tiết gói R-Point')}
      size="lg"
    >
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">{t('common:loading', 'Đang tải...')}</span>
        </div>
      ) : pointDetail ? (
        <Card>
          <div className="space-y-6">
            {/* Tên gói point */}
            <FormItem
              name="name"
              label={t('rpointAdmin:points.form.name')}
              required
            >
              <Input
                value={pointDetail.name}
                placeholder={t('rpointAdmin:points.form.name')}
                fullWidth
                readOnly
              />
            </FormItem>

            {/* Checkbox Customize */}
            <div>
              <FormItem
                name="isCustomize"
                label=""
              >
                <Checkbox
                  label={t('rpointAdmin:points.form.isCustomize', 'Customize')}
                  checked={pointDetail.isCustomize}
                  disabled
                />
              </FormItem>
            </div>

            {/* Thông tin giá và point - Layout giống PointForm */}
            {!pointDetail.isCustomize ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                  name="cash"
                  label={t('rpointAdmin:points.form.cash')}
                  required
                >
                  <Input
                    type="text"
                    value={`${formatCurrency(pointDetail.cash)} VND`}
                    placeholder={t('rpointAdmin:points.form.cash')}
                    fullWidth
                    readOnly
                    className="font-semibold text-green-600"
                  />
                </FormItem>

                <FormItem
                  name="point"
                  label={t('rpointAdmin:points.form.point')}
                  required
                >
                  <Input
                    type="text"
                    value={`${pointDetail.point} Point`}
                    placeholder={t('rpointAdmin:points.form.point')}
                    fullWidth
                    readOnly
                    className="font-semibold text-primary"
                  />
                </FormItem>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormItem
                  name="rate"
                  label={t('rpointAdmin:points.form.rate', 'Rate')}
                  required
                >
                  <Input
                    type="text"
                    value={`1:${pointDetail.rate}`}
                    placeholder={t('rpointAdmin:points.form.rate', 'Rate')}
                    fullWidth
                    readOnly
                  />
                </FormItem>

                <FormItem
                  name="min"
                  label={t('rpointAdmin:points.form.min', 'Min')}
                  required
                >
                  <Input
                    type="text"
                    value={pointDetail.min ? `${formatCurrency(pointDetail.min)} VND` : 'N/A'}
                    placeholder={t('rpointAdmin:points.form.min', 'Min')}
                    fullWidth
                    readOnly
                  />
                </FormItem>

                <FormItem
                  name="max"
                  label={t('rpointAdmin:points.form.max', 'Max')}
                  required
                >
                  <Input
                    type="text"
                    value={pointDetail.max ? `${formatCurrency(pointDetail.max)} VND` : 'N/A'}
                    placeholder={t('rpointAdmin:points.form.max', 'Max')}
                    fullWidth
                    readOnly
                  />
                </FormItem>
              </div>
            )}

            {/* Mô tả */}
            <FormItem
              name="description"
              label={t('rpointAdmin:points.form.description')}
              required
            >
              <Textarea
                value={pointDetail.description || ''}
                placeholder={t('rpointAdmin:points.form.description')}
                rows={4}
                fullWidth
                readOnly
              />
            </FormItem>

            {/* Thông tin bổ sung - ID */}
            <FormItem
              name="id"
              label="ID"
            >
              <Input
                type="text"
                value={pointDetail.id.toString()}
                placeholder="ID"
                fullWidth
                readOnly
                className="text-gray-500"
              />
            </FormItem>
          </div>
        </Card>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 dark:text-gray-400">
            {t('common:noData', 'Không có dữ liệu')}
          </p>
        </div>
      )}
    </Modal>
  );
};

export default PointDetailModal;
