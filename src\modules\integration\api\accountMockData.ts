import { BankAccount } from '../types/account';

/**
 * Tạo danh sách tài khoản ngân hàng mẫu
 */
const generateMockAccounts = (count: number): BankAccount[] => {
  const banks = [
    { code: 'MB', name: 'MB Bank' },
    { code: 'VCB', name: 'Vietcombank' },
    { code: 'TCB', name: 'Techcombank' },
    { code: 'ACB', name: 'ACB Bank' },
    { code: 'VPB', name: 'VPBank' },
    { code: 'OCB', name: 'OCB Bank' },
    { code: 'KIENLONG', name: 'Kiên Long Bank' },
  ];

  const accountNames = [
    'PHAM THI HUONG GIANG',
    'NGUYEN VAN MINH',
    'TRAN THI THANH',
    'LE QUANG HUNG',
    'HOANG MINH TUAN',
  ];

  return Array.from({ length: count }).map((_, index) => {
    const id = `acc-${index + 1}`;
    const bankIndex = index % banks.length;
    const bank = banks[bankIndex];
    const nameIndex = index % accountNames.length;
    const accountName = accountNames[nameIndex];
    const accountNumber = `${Math.floor(******** + Math.random() * ********)}****`;

    // Tạo ngày liên kết trong khoảng 90 ngày gần đây
    const linkedDate = new Date(
      Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000
    ).toISOString();

    return {
      id,
      bankCode: bank.code,
      bankName: bank.name,
      accountName,
      accountNumber,
      logoUrl: `/src/modules/integration/assets/${bank.code.toLowerCase()}-logo.svg`,
      linkedDate,
      isDefault: index === 0, // Tài khoản đầu tiên là mặc định
    };
  });
};

// Tạo danh sách tài khoản mẫu
const mockAccounts = generateMockAccounts(7);

/**
 * Lấy danh sách tài khoản đã liên kết
 */
export const fetchLinkedAccounts = async (bankCode?: string): Promise<BankAccount[]> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 800));

  // Lọc theo mã ngân hàng nếu có
  let filteredAccounts = [...mockAccounts];

  if (bankCode) {
    filteredAccounts = filteredAccounts.filter(account => account.bankCode === bankCode);
  }

  return filteredAccounts;
};

/**
 * Lấy thông tin chi tiết của tài khoản theo ID
 */
export const fetchAccountById = async (id: string): Promise<BankAccount | null> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 500));

  const account = mockAccounts.find(acc => acc.id === id);
  return account || null;
};

/**
 * Thêm tài khoản mới
 */
export const addLinkedAccount = async (
  account: Omit<BankAccount, 'id' | 'linkedDate'>
): Promise<BankAccount> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Tạo tài khoản mới
  const newAccount: BankAccount = {
    ...account,
    id: `acc-${mockAccounts.length + 1}`,
    linkedDate: new Date().toISOString(),
  };

  // Thêm vào danh sách
  mockAccounts.push(newAccount);

  return newAccount;
};

/**
 * Xóa tài khoản theo ID
 */
export const removeLinkedAccount = async (id: string): Promise<boolean> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 800));

  const index = mockAccounts.findIndex(account => account.id === id);

  if (index !== -1) {
    mockAccounts.splice(index, 1);
    return true;
  }

  return false;
};

/**
 * Đặt tài khoản mặc định
 */
export const setDefaultAccount = async (id: string): Promise<boolean> => {
  // Giả lập độ trễ của API
  await new Promise(resolve => setTimeout(resolve, 600));

  // Tìm tài khoản cần đặt làm mặc định
  const account = mockAccounts.find(acc => acc.id === id);

  if (!account) {
    return false;
  }

  // Bỏ mặc định tất cả các tài khoản
  mockAccounts.forEach(acc => {
    acc.isDefault = false;
  });

  // Đặt tài khoản được chọn làm mặc định
  account.isDefault = true;

  return true;
};
