import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Loading,
  Icon,
} from '@/shared/components/common';
import { useConversion } from '../hooks/useConversionQuery';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

/**
 * Trang chi tiết chuyển đổi
 */
const ConversionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['business', 'common']);

  const conversionId = id ? parseInt(id, 10) : 0;
  const { data: conversion, isLoading, error } = useConversion(conversionId);

  const handleBack = () => {
    navigate('/business/conversion');
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !conversion) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <Icon name="alert-circle" size="lg" className="text-red-500" />
        <Typography variant="h6" className="text-red-500">
          {t('business:conversion.detail.error', 'Không thể tải thông tin chuyển đổi')}
        </Typography>
        <Button variant="outline" onClick={handleBack}>
          {t('common:back', 'Quay lại')}
        </Button>
      </div>
    );
  }

  const formatDate = (timestamp: number) => {
    return format(new Date(timestamp), 'dd/MM/yyyy HH:mm', { locale: vi });
  };



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <Icon name="arrow-left" size="sm" className="mr-2" />
            {t('common:back', 'Quay lại')}
          </Button>
          <Typography variant="h5">
            {t('business:conversion.detail.title', 'Chi tiết chuyển đổi')} #{conversion.id}
          </Typography>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversion Info */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('business:conversion.detail.conversionInfo', 'Thông tin chuyển đổi')}
              </Typography>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:conversion.conversionType', 'Loại chuyển đổi')}
                  </Typography>
                  <Typography variant="body1">
                    {conversion.conversionType || t('common:notSet', 'Chưa thiết lập')}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:conversion.source', 'Nguồn')}
                  </Typography>
                  <Typography variant="body1">
                    {conversion.source || t('common:notSet', 'Chưa thiết lập')}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:conversion.createdAt', 'Thời gian tạo')}
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(conversion.createdAt)}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:conversion.updatedAt', 'Cập nhật lần cuối')}
                  </Typography>
                  <Typography variant="body1">
                    {formatDate(conversion.updatedAt)}
                  </Typography>
                </div>
              </div>

              {conversion.notes && (
                <div className="mt-4">
                  <Typography variant="caption" className="text-gray-500 mb-1">
                    {t('business:conversion.notes', 'Ghi chú')}
                  </Typography>
                  <Typography variant="body1">
                    {conversion.notes}
                  </Typography>
                </div>
              )}
            </div>
          </Card>

          {/* Additional Content */}
          {conversion.content && Object.keys(conversion.content).length > 0 && (
            <Card>
              <div className="p-6">
                <Typography variant="h6" className="mb-4">
                  {t('business:conversion.detail.additionalInfo', 'Thông tin bổ sung')}
                </Typography>

                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                    {JSON.stringify(conversion.content, null, 2)}
                  </pre>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Customer Info */}
        <div className="space-y-6">
          {conversion.convertCustomer && (
            <Card>
              <div className="p-6">
                <Typography variant="h6" className="mb-4">
                  {t('business:conversion.detail.customerInfo', 'Thông tin khách hàng')}
                </Typography>

                <div className="space-y-3">
                  <div>
                    <Typography variant="caption" className="text-gray-500 mb-1">
                      {t('business:customer.name', 'Tên khách hàng')}
                    </Typography>
                    <Typography variant="body1">
                      {conversion.convertCustomer.name || t('common:notSet', 'Chưa thiết lập')}
                    </Typography>
                  </div>

                  {conversion.convertCustomer.email && (
                    <div>
                      <Typography variant="caption" className="text-gray-500 mb-1">
                        {t('business:customer.email', 'Email')}
                      </Typography>
                      <Typography variant="body1">
                        {typeof conversion.convertCustomer.email === 'object'
                          ? (conversion.convertCustomer.email as Record<string, unknown>)['primary'] as string || JSON.stringify(conversion.convertCustomer.email)
                          : conversion.convertCustomer.email
                        }
                      </Typography>
                    </div>
                  )}

                  {conversion.convertCustomer.phone && (
                    <div>
                      <Typography variant="caption" className="text-gray-500 mb-1">
                        {t('business:customer.phone', 'Số điện thoại')}
                      </Typography>
                      <Typography variant="body1">
                        {conversion.convertCustomer.phone}
                      </Typography>
                    </div>
                  )}

                  {conversion.convertCustomer.platform && (
                    <div>
                      <Typography variant="caption" className="text-gray-500 mb-1">
                        {t('business:customer.platform', 'Nền tảng')}
                      </Typography>
                      <Typography variant="body1">
                        {conversion.convertCustomer.platform}
                      </Typography>
                    </div>
                  )}

                  <div>
                    <Typography variant="caption" className="text-gray-500 mb-1">
                      {t('business:customer.createdAt', 'Ngày tạo')}
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(conversion.convertCustomer.createdAt)}
                    </Typography>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          <Card>
            <div className="p-6">
              <Typography variant="h6" className="mb-4">
                {t('common:actions', 'Thao tác')}
              </Typography>

              <div className="space-y-2">
                <Button variant="outline" fullWidth onClick={handleBack}>
                  <Icon name="arrow-left" size="sm" className="mr-2" />
                  {t('common:back', 'Quay lại danh sách')}
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ConversionDetailPage;
