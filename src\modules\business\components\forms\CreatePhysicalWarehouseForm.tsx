import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import {
  Card,
  Form,
  FormItem,
  Input,
  Select,
  IconCard,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useCreatePhysicalWarehouse } from '../../hooks/usePhysicalWarehouseQuery';
import { useWarehouses } from '../../hooks/useWarehouseQuery';
import { CreatePhysicalWarehouseDto } from '../../types/physical-warehouse.types';
import { createPhysicalWarehouseSchema, CreatePhysicalWarehouseFormValues } from '../../schemas/physical-warehouse.schema';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

interface CreatePhysicalWarehouseFormProps {
  initialData?: Partial<CreatePhysicalWarehouseFormValues>;
  onSubmit?: (data: CreatePhysicalWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form tạo kho vật lý mới
 */
const CreatePhysicalWarehouseForm: React.FC<CreatePhysicalWarehouseFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();
  const formRef = useRef<FormRef<FieldValues> | null>(null);

  // Mutation để tạo kho vật lý mới
  const { mutateAsync: createPhysicalWarehouse, isPending: isCreating } = useCreatePhysicalWarehouse();

  // Lấy danh sách warehouses để chọn
  const { data: warehousesData } = useWarehouses({
    page: 1,
    limit: 100, // Lấy nhiều để có đủ options
  });

  // Giá trị mặc định cho form
  const defaultValues = {
    warehouseId: initialData?.warehouseId || '', // Sử dụng string rỗng để tránh lỗi validation
    address: initialData?.address || '',
    capacity: initialData?.capacity || '',
  };

  // Xử lý submit form
  const handleSubmit = async (values: CreatePhysicalWarehouseFormValues) => {
    try {
      // Chuyển đổi dữ liệu trước khi gửi
      const submitData: CreatePhysicalWarehouseDto = {
        warehouseId: typeof values.warehouseId === 'string'
          ? Number(values.warehouseId)
          : values.warehouseId,
        address: values.address,
        ...(values.capacity && {
          capacity: typeof values.capacity === 'string' ? Number(values.capacity) : values.capacity
        }),
      };

      // Tạo kho vật lý mới
      await createPhysicalWarehouse(submitData);
      notification.success({ message: t('business:physicalWarehouse.createSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('Error creating physical warehouse:', error);
      notification.error({
        message: t('business:physicalWarehouse.createError')
      });
    }
  };

  // Tạo options cho warehouse select
  const warehouseOptions = warehousesData?.result?.items?.map(warehouse => ({
    value: warehouse.warehouseId,
    label: warehouse.name,
  })) || [];

  return (
    <Card title={t('business:physicalWarehouse.add')}>
      <Form
        ref={formRef}
        schema={createPhysicalWarehouseSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={defaultValues}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem
            name="warehouseId"
            label={t('business:physicalWarehouse.warehouse')}
            required
          >
            <Select
              fullWidth
              placeholder={t('business:physicalWarehouse.form.selectWarehouse')}
              options={warehouseOptions}
            />
          </FormItem>

          <FormItem
            name="address"
            label={t('business:physicalWarehouse.address')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:physicalWarehouse.form.addressPlaceholder')}
            />
          </FormItem>

          <FormItem
            name="capacity"
            label={t('business:physicalWarehouse.capacity')}
          >
            <Input
              type="number"
              fullWidth
              placeholder={t('business:physicalWarehouse.form.capacityPlaceholder')}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={isCreating}
          />
          <IconCard
            icon="save"
            variant="primary"
            size="md"
            title={t('common:create')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={isCreating}
            isLoading={isCreating}
          />
        </div>
      </Form>
    </Card>
  );
};

export default CreatePhysicalWarehouseForm;
