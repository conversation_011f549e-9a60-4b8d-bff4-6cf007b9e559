import { apiClient as apiRequest } from '@/shared/api/';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  BusinessApiService,
  ApiUserOrderListItem,
  ApiUserOrderStatusResponse,
  OrderQueryParams as ApiOrderQueryParams,
  CreateUserOrderDto as ApiCreateUserOrderDto,
  OrderStatusEnum,
  PaymentStatusEnum,
  PaymentMethodEnum,
  ShippingMethodEnum,
  ShippingStatusEnum
} from './business-api.service';
import { ShippingMethod } from '../types/order.types';
import { ProductTypeEnum } from '../types/product.types';

// All imports above are used in this file

/**
 * Enum cho trạng thái đơn hàng (legacy - sử dụng OrderStatusEnum từ business-api.service)
 * @deprecated Use OrderStatusEnum from business-api.service instead
 */
export enum OrderStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

/**
 * Enum cho phương thức thanh toán (legacy - sử dụng PaymentMethodEnum từ business-api.service)
 * @deprecated Use PaymentMethodEnum from business-api.service instead
 */
export enum PaymentMethod {
  CASH = 'CASH',
  CREDIT_CARD = 'CREDIT_CARD',
  BANK_TRANSFER = 'BANKING', // Map to backend BANKING value
  DIGITAL_WALLET = 'E_WALLET', // Map to backend E_WALLET value
}

/**
 * Interface cho thông tin khách hàng
 */
export interface CustomerInfo {
  id: number;
  name: string;
  email: string;
  phone: string;
  address?: string;
}

/**
 * Interface cho sản phẩm trong đơn hàng
 */
export interface OrderItem {
  id: number;
  productId: number;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

/**
 * Interface cho đơn hàng
 */
export interface Order {
  id: number;
  orderNumber: string;
  customerId: number;
  customerInfo: CustomerInfo;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  paymentMethod: PaymentMethod;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  createdAt: number;
  updatedAt: number;
  notes?: string;
}

/**
 * Interface cho danh sách đơn hàng (dựa trên API thật)
 */
export interface OrderListItem {
  id: string;
  userConvertCustomer: {
    id: string;
    avatar?: string;
    name: string;
    email: {
      primary: string;
      secondary?: string;
    };
    phone: string;
    platform?: string;
    timezone?: string;
    createdAt: string;
    updatedAt: string;
    address?: string;
    metadata?: Record<string, unknown>;
  };
  billInfo: {
    total: number;
    subtotal: number;
    shippingFee: number;
    paymentMethod: string;
    paymentStatus: string;
    selectedCarrier?: string;
    shippingServiceType?: string;
  };
  shippingStatus: string;
  createdAt: string;
  source: string;
  orderStatus: string;
}

/**
 * Interface cho danh sách đơn hàng đã được transform
 */
export interface TransformedOrderListItem {
  id: string;
  orderNumber: string;
  customerName: string;
  totalAmount: number;
  status: OrderStatus;
  paymentStatus: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  shippingStatus: string;
  createdAt: number;
  source: string;
}

/**
 * Interface cho tham số truy vấn đơn hàng (legacy)
 * @deprecated Use ApiOrderQueryParams from business-api.service instead
 */
export interface OrderQueryParams {
  page?: number;
  limit?: number;
  search?: string | undefined;
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  fromDate?: string | undefined;
  toDate?: string | undefined;
  sortBy?: string | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
  userConvertCustomerId?: string;
}

/**
 * Interface cho dữ liệu tạo đơn hàng
 */
export interface CreateOrderData {
  customerId: number;
  items: Array<{
    productId: number;
    quantity: number;
    price: number;
    productType?: import('../types/product.types').ProductTypeEnum;
  }>;
  paymentMethod: PaymentMethod;
  notes?: string | undefined;
  shipping?: {
    method: ShippingMethod;
    serviceId?: number;
    serviceName?: string;
    fromAddress: import('../types/order.types').AddressDto;
    toAddress: import('../types/order.types').AddressDto;
    fee: number;
    estimatedDelivery?: string;
    note?: string;
    addressId?: number; // ID của địa chỉ giao hàng đã chọn
  };
  digitalDelivery?: {
    method: import('../types/order.types').DigitalDeliveryMethod;
    recipient: string;
    message?: string;
    scheduledDelivery?: string;
  };
  payment?: {
    method: string;
    status: string;
    codAmount?: number;
  };
  tags?: string[];
}

/**
 * Interface cho dữ liệu cập nhật đơn hàng
 */
export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: 'PAID' | 'UNPAID' | 'PARTIALLY_PAID';
  notes?: string;
}

/**
 * Hàm transform dữ liệu từ API thành format hiển thị (updated for new API)
 */
export const transformOrderListItem = (item: ApiUserOrderListItem): TransformedOrderListItem => {
  // Map payment status
  const getPaymentStatus = (paymentStatus: PaymentStatusEnum): 'PAID' | 'UNPAID' | 'PARTIALLY_PAID' => {
    switch (paymentStatus) {
      case PaymentStatusEnum.PAID:
        return 'PAID';
      case PaymentStatusEnum.PENDING:
      case PaymentStatusEnum.FAILED:
        return 'UNPAID';
      case PaymentStatusEnum.REFUNDED:
        return 'PARTIALLY_PAID';
      default:
        return 'UNPAID';
    }
  };

  // Map order status
  const getOrderStatus = (status: OrderStatusEnum): OrderStatus => {
    switch (status) {
      case OrderStatusEnum.PENDING:
        return OrderStatus.PENDING;
      case OrderStatusEnum.CONFIRMED:
      case OrderStatusEnum.PROCESSING:
        return OrderStatus.PROCESSING;
      case OrderStatusEnum.COMPLETED:
        return OrderStatus.COMPLETED;
      case OrderStatusEnum.CANCELLED:
        return OrderStatus.CANCELLED;
      default:
        return OrderStatus.PENDING;
    }
  };

  return {
    id: item.id.toString(),
    orderNumber: `#${item.id}`,
    customerName: item.userConvertCustomer?.name || `Khách hàng ${item.userConvertCustomer?.id}`,
    totalAmount: item.billInfo.total,
    status: getOrderStatus(item.orderStatus),
    paymentStatus: getPaymentStatus(item.billInfo.paymentStatus),
    shippingStatus: item.shippingStatus,
    createdAt: item.createdAt,
    source: item.source,
  };
};

/**
 * Helper function để map PaymentMethod legacy sang PaymentMethodEnum mới
 */
const mapPaymentMethodToEnum = (paymentMethod: PaymentMethod): PaymentMethodEnum => {
  switch (paymentMethod) {
    case PaymentMethod.CASH:
      return PaymentMethodEnum.CASH;
    case PaymentMethod.CREDIT_CARD:
      return PaymentMethodEnum.CREDIT_CARD;
    case PaymentMethod.BANK_TRANSFER:
      return PaymentMethodEnum.BANKING;
    case PaymentMethod.DIGITAL_WALLET:
      return PaymentMethodEnum.E_WALLET;
    default:
      return PaymentMethodEnum.CASH; // Default fallback
  }
};

/**
 * Service xử lý API liên quan đến đơn hàng (updated to use BusinessApiService)
 */
export const OrderService = {
  /**
   * Lấy danh sách đơn hàng
   * @param params Tham số truy vấn
   * @returns Danh sách đơn hàng với phân trang
   */
  getOrders: async (params?: OrderQueryParams): Promise<ApiResponseDto<PaginatedResult<ApiUserOrderListItem>>> => {
    // Transform legacy params to new API params
    const apiParams: ApiOrderQueryParams = {};

    // Chỉ thêm các property khi có giá trị thực sự
    if (params?.page !== undefined) {
      apiParams.page = params.page;
    }
    if (params?.limit !== undefined) {
      apiParams.limit = params.limit;
    }
    if (params?.userConvertCustomerId) {
      apiParams.userConvertCustomerId = parseInt(params.userConvertCustomerId);
    }
    if (params?.sortBy) {
      apiParams.sortBy = params.sortBy as 'createdAt' | 'updatedAt' | 'shippingStatus' | 'orderStatus';
    }
    if (params?.sortDirection) {
      apiParams.sortDirection = params.sortDirection;
    }

    // Map legacy status to new enum
    if (params?.status) {
      switch (params.status) {
        case OrderStatus.PENDING:
          apiParams.orderStatus = OrderStatusEnum.PENDING;
          break;
        case OrderStatus.PROCESSING:
          apiParams.orderStatus = OrderStatusEnum.PROCESSING;
          break;
        case OrderStatus.COMPLETED:
          apiParams.orderStatus = OrderStatusEnum.COMPLETED;
          break;
        case OrderStatus.CANCELLED:
          apiParams.orderStatus = OrderStatusEnum.CANCELLED;
          break;
      }
    }

    const result = await BusinessApiService.getOrders(apiParams);

    // Return the actual API response format
    return {
      result: result,
      message: 'Success',
      code: 200,
    } as ApiResponseDto<PaginatedResult<ApiUserOrderListItem>>;
  },

  /**
   * Lấy chi tiết đơn hàng theo ID
   * @param id ID của đơn hàng
   * @returns Chi tiết đơn hàng
   */
  getOrderById: async (id: number): Promise<ApiResponseDto<Order>> => {
    const result = await BusinessApiService.getOrderDetail(id);

    // Transform to legacy format for backward compatibility
    return {
      result: result as unknown as Order,
      message: 'Success',
      code: 200,
    } as ApiResponseDto<Order>;
  },

  /**
   * Tạo đơn hàng mới
   * @param data Dữ liệu tạo đơn hàng
   * @returns Thông tin đơn hàng đã tạo
   */
  createOrder: async (data: CreateOrderData): Promise<ApiResponseDto<Order>> => {
    // Calculate subtotal from items
    const subtotal = data.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // Kiểm tra có sản phẩm nào cần vận chuyển không
    const needsShipping = data.items.some(item => {
      const productType = item.productType;
      // Chỉ PHYSICAL và COMBO (có chứa sản phẩm vật lý) mới cần vận chuyển
      // Nếu không có productType, mặc định coi là PHYSICAL (backward compatibility)
      if (!productType) return true;
      return productType === ProductTypeEnum.PHYSICAL ||
             productType === ProductTypeEnum.COMBO;
    });

    // Chỉ tính shipping fee khi thực sự cần vận chuyển
    const shippingFee = needsShipping ? (data.shipping?.fee || 0) : 0;

    // Tính tổng tiền cuối cùng
    const finalTotal = subtotal + shippingFee;

    // Workaround: Backend validation yêu cầu total > 0, nhưng đơn hàng có thể có giá 0đ hợp lệ
    // Nếu total = 0, set thành 1 VND để pass validation
    const adjustedSubtotal = subtotal === 0 ? 1 : subtotal;
    const adjustedTotal = finalTotal === 0 ? 1 : finalTotal;

    // Transform legacy data to new API format matching the sample body
    const apiData: ApiCreateUserOrderDto = {
      shopId: 1, // Default shop ID, should be configurable
      customerInfo: {
        customerId: Number(data.customerId), // Ensure it's a number
      },
      products: data.items.map(item => ({
        productId: Number(item.productId), // Ensure it's a number
        quantity: Number(item.quantity), // Ensure it's a number
      })),
      billInfo: {
        subtotal: adjustedSubtotal,
        discount: 0, // TODO: Add discount calculation
        paymentMethod: mapPaymentMethodToEnum(data.paymentMethod),
        total: adjustedTotal, // Thêm total để tránh backend tự tính và có thể gây lỗi
        // Chỉ thêm shipping-related fields khi cần thiết
        ...(needsShipping && {
          shippingFee: shippingFee,
          shippingServiceType: data.shipping?.serviceName || 'standard',
        }),
      },
      // Set hasShipping dựa trên loại sản phẩm
      hasShipping: needsShipping,
      // Chỉ set shippingStatus khi cần vận chuyển
      ...(needsShipping && {
        shippingStatus: ShippingStatusEnum.PENDING,
      }),
      source: 'website',
    };

    // Chỉ thêm shipping-related properties khi thực sự cần vận chuyển
    if (needsShipping && data.shipping) {
      // Thêm selectedCarrier vào billInfo
      if (data.shipping.method === ShippingMethod.GHN || data.shipping.method === ShippingMethod.GHTK) {
        apiData.billInfo.selectedCarrier = data.shipping.method === ShippingMethod.GHN ? 'GHN' : 'GHTK';
      }

      // Thêm logisticInfo
      apiData.logisticInfo = {
        shippingMethod: data.shipping.method === ShippingMethod.GHN ? ShippingMethodEnum.GHN_STANDARD :
                       data.shipping.method === ShippingMethod.GHTK ? ShippingMethodEnum.GHTK_ROAD :
                       ShippingMethodEnum.GHN_STANDARD, // Default to GHN Standard
        carrier: data.shipping.method === ShippingMethod.GHN ? 'GHN' :
                data.shipping.method === ShippingMethod.GHTK ? 'GHTK' : 'SELF',
        shippingNote: data.shipping.note || 'Giao hàng trong giờ hành chính',
        deliveryAddress: {
          // Ưu tiên sử dụng addressId nếu có (địa chỉ đã chọn từ danh sách)
          ...(data.shipping.addressId && { addressId: data.shipping.addressId }),
          // Nếu không có addressId, sử dụng thông tin địa chỉ chi tiết từ toAddress
          ...(data.shipping.toAddress && !data.shipping.addressId && {
            address: data.shipping.toAddress.address,
            province: data.shipping.toAddress.province,
            district: data.shipping.toAddress.district,
            ward: data.shipping.toAddress.ward,
            provinceId: data.shipping.toAddress.provinceId,
            districtId: data.shipping.toAddress.districtId,
            wardCode: data.shipping.toAddress.wardCode,
          }),
          // Fallback: sử dụng customerId để lấy địa chỉ mặc định
          ...(!data.shipping.addressId && !data.shipping.toAddress && {
            // Sử dụng customerId để backend tự động lấy địa chỉ mặc định
            // Nếu không có customerId, fallback về addressId = 1
            ...(data.customerId ? {} : { addressId: 1 })
          }),
        },
      };
    }

    if (data.notes) {
      apiData.note = data.notes;
    }

    if (data.tags && data.tags.length > 0) {
      apiData.tags = data.tags;
    }

    console.log('🔍 [OrderService] Creating order with API data:', JSON.stringify(apiData, null, 2));
    console.log('🔍 [OrderService] Order analysis:', {
      needsShipping,
      hasShippingData: !!data.shipping,
      productTypes: data.items.map(item => item.productType),
      originalSubtotal: subtotal,
      adjustedSubtotal,
      shippingFee,
      originalTotal: finalTotal,
      adjustedTotal,
      hasShippingInBillInfo: !!apiData.billInfo.shippingFee,
      hasLogisticInfo: !!apiData.logisticInfo
    });
    console.log('🔍 [OrderService] Data types check:', {
      customerId: typeof apiData.customerInfo.customerId,
      productId: typeof apiData.products[0]?.productId,
      quantity: typeof apiData.products[0]?.quantity,
      hasShipping: apiData.hasShipping,
      shippingStatus: apiData.shippingStatus,
      shippingMethod: typeof apiData.logisticInfo?.shippingMethod,
      shippingMethodValue: apiData.logisticInfo?.shippingMethod,
      deliveryAddress: apiData.logisticInfo?.deliveryAddress
    });

    const result = await BusinessApiService.createOrder(apiData);

    console.log('✅ [OrderService] Order created successfully:', result);

    // Transform to legacy format for backward compatibility
    return {
      result: result as unknown as Order,
      message: 'Success',
      code: 201,
    } as ApiResponseDto<Order>;
  },

  /**
   * Cập nhật đơn hàng
   * @param id ID của đơn hàng
   * @param data Dữ liệu cập nhật đơn hàng
   * @returns Thông tin đơn hàng đã cập nhật
   */
  updateOrder: async (id: number, data: UpdateOrderData): Promise<ApiResponseDto<Order>> => {
    // Note: Update API not implemented in BusinessApiService yet
    return apiRequest.put(`/user/orders/${id}`, data);
  },

  /**
   * Xóa đơn hàng
   * @param id ID của đơn hàng
   * @returns Thông báo xóa thành công
   */
  deleteOrder: async (id: number): Promise<ApiResponseDto<null>> => {
    // Note: Delete API not implemented in BusinessApiService yet
    return apiRequest.delete(`/user/orders/${id}`);
  },

  /**
   * Lấy thống kê trạng thái đơn hàng
   * @returns Thống kê trạng thái đơn hàng
   */
  getOrderStatusStats: async (): Promise<ApiResponseDto<ApiUserOrderStatusResponse>> => {
    const result = await BusinessApiService.getOrderStatusStats();

    return {
      result: result,
      message: 'Success',
      code: 200,
    } as ApiResponseDto<ApiUserOrderStatusResponse>;
  },

  /**
   * Tracking đơn hàng
   * @param id ID của đơn hàng
   * @returns Thông tin tracking
   */
  trackOrder: async (id: number): Promise<ApiResponseDto<unknown>> => {
    const result = await BusinessApiService.trackOrder(id);

    return {
      result: result,
      message: 'Success',
      code: 200,
    } as ApiResponseDto<unknown>;
  },
};
