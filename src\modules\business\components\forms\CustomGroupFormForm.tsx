import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  IconCard,
  Typography,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

export interface CustomGroupFormValues {
  label: string;
  description?: string | undefined;
}

interface CustomGroupFormFormProps {
  formRef: React.RefObject<FormRef<CustomGroupFormValues>>;
  onSubmit: (values: CustomGroupFormValues) => void;
  onCancel: () => void;
  title: string;
  initialData?: Partial<CustomGroupFormValues> | undefined;
  isLoading?: boolean | undefined;
}

/**
 * Form component cho nhóm trường tùy chỉnh
 */
const CustomGroupFormForm: React.FC<CustomGroupFormFormProps> = ({
  formRef,
  onSubmit,
  onCancel,
  title,
  initialData,
  isLoading = false,
}) => {
  const { t } = useTranslation('business');

  const handleSubmit = (formData: CustomGroupFormValues) => {
    // Validation
    const errors: Partial<CustomGroupFormValues> = {};

    if (!formData.label?.trim()) {
      errors.label = t('customGroupForm.form.validation.labelRequired');
    } else if (formData.label.trim().length < 2) {
      errors.label = t('customGroupForm.form.validation.labelMinLength');
    } else if (formData.label.trim().length > 255) {
      errors.label = t('customGroupForm.form.validation.labelMaxLength');
    }

    if (formData.description && formData.description.trim().length > 500) {
      errors.description = t('customGroupForm.form.validation.descriptionMaxLength');
    }

    if (Object.keys(errors).length > 0) {
      if (formRef.current) {
        formRef.current.setErrors(errors);
      }
      return;
    }

    // Submit valid data
    const trimmedDescription = formData.description?.trim();
    const submitData: CustomGroupFormValues = {
      label: formData.label.trim(),
    };

    // Only include description if it has a value
    if (trimmedDescription) {
      submitData.description = trimmedDescription;
    }

    onSubmit(submitData);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h5" className="text-foreground mb-2">
          {title}
        </Typography>
        <Typography variant="body2" className="text-muted">
          {t('customGroupForm.form.subtitle')}
        </Typography>
      </div>

      {/* Form */}
      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        defaultValues={{
          label: initialData?.label || '',
          description: initialData?.description || '',
        }}
        submitOnEnter={false}
      >
        <div className="space-y-6">
          {/* Tên nhóm */}
          <FormItem
            name="label"
            label={t('customGroupForm.form.label')}
            required
          >
            <Input
              placeholder={t('customGroupForm.form.labelPlaceholder')}
              fullWidth
              disabled={isLoading}
            />
          </FormItem>

          {/* Mô tả */}
          <FormItem
            name="description"
            label={t('customGroupForm.form.description')}
          >
            <Textarea
              placeholder={t('customGroupForm.form.descriptionPlaceholder')}
              fullWidth
              rows={4}
              disabled={isLoading}
            />
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-border">
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('common.cancel')}
              onClick={onCancel}
              disabled={isLoading}
            />
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={isLoading ? t('common.saving') : t('common.save')}
              onClick={() => {
                // Trigger form submit programmatically
                formRef.current?.submit();
              }}
              disabled={isLoading}
              isLoading={isLoading}
            />
          </div>
        </div>
      </Form>
    </div>
  );
};

export default CustomGroupFormForm;
