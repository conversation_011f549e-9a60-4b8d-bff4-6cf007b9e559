import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
  DateTimePicker,
  AvatarImageUploader,
} from '@/shared/components/common';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  UpdateProductDto,
  ProductDto,
  EventTicketType,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useUpdateProduct } from '../../hooks/useProductQuery';

interface EventProductEditFormProps {
  productId: number;
  product: ProductDto;
  onCancel: () => void;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho loại vé trong form (có thêm id và các field khác cho UI)
interface FormEventTicketType extends Partial<EventTicketType> {
  id: string; // ID tạm thời cho quản lý state
  name: string;
  price: number;
  currency?: string;
  totalTickets?: number;
  saleStartTime?: Date | undefined;
  saleEndTime?: Date | undefined;
  ticketImage?: string;
  sku?: string;
  minQuantityPerOrder?: number;
  maxQuantityPerOrder?: number;
  description?: string;
}

// Interface cho form values
interface EventProductFormValues {
  name: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Event product specific fields
  eventDateTime?: Date;
  eventLocation?: string;
  attendanceMode: 'ONLINE' | 'OFFLINE';
  zoomLink?: string;
  ticketTypes: FormEventTicketType[];
}

/**
 * Form chỉnh sửa sự kiện
 */
const EventProductEditForm: React.FC<EventProductEditFormProps> = ({ productId, product, onCancel }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation cho sự kiện
  const eventProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sự kiện không được để trống'),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Event product specific validations
      eventDateTime: z.date().optional(),
      eventLocation: z.string().optional(),
      attendanceMode: z.enum(['ONLINE', 'OFFLINE']),
      zoomLink: z.string().url('Link Zoom không hợp lệ').optional().or(z.literal('')),
      ticketTypes: z.array(z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Tên loại vé không được để trống'),
        price: z.number().min(0, 'Giá vé phải >= 0'),
        currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
        totalTickets: z.number().min(1, 'Tổng số vé phải >= 1'),
        saleStartTime: z.date().optional(),
        saleEndTime: z.date().optional(),
        ticketImage: z.string().optional(),
        sku: z.string().optional(),
        minQuantityPerOrder: z.number().min(1, 'Số vé tối thiểu phải >= 1'),
        maxQuantityPerOrder: z.number().min(1, 'Số vé tối đa phải >= 1'),
        description: z.string().optional(),
      })).min(1, 'Phải có ít nhất 1 loại vé'),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra địa điểm sự kiện cho offline events
      if (data.attendanceMode === 'OFFLINE' && (!data.eventLocation || data.eventLocation.trim() === '')) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập địa điểm sự kiện cho hình thức offline',
          path: ['eventLocation'],
        });
      }

      // Kiểm tra Zoom link cho sự kiện online
      if (data.attendanceMode === 'ONLINE' && data.zoomLink && data.zoomLink.trim() !== '') {
        try {
          new URL(data.zoomLink);
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Link Zoom không hợp lệ',
            path: ['zoomLink'],
          });
        }
      }

      // Kiểm tra ticket types
      if (!data.ticketTypes || data.ticketTypes.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Phải có ít nhất 1 loại vé',
          path: ['ticketTypes'],
        });
      } else {
        data.ticketTypes.forEach((ticket, index) => {
          if (!ticket.name || ticket.name.trim() === '') {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Tên loại vé ${index + 1} không được để trống`,
              path: ['ticketTypes', index, 'name'],
            });
          }
          if ((ticket.maxQuantityPerOrder || 0) < (ticket.minQuantityPerOrder || 0)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: `Số vé tối đa phải >= số vé tối thiểu cho loại vé ${index + 1}`,
              path: ['ticketTypes', index, 'maxQuantityPerOrder'],
            });
          }
        });
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho loại vé sự kiện
  const [ticketTypes, setTicketTypes] = useState<FormEventTicketType[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Hook để cập nhật sản phẩm
  const updateProductMutation = useUpdateProduct();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Đồng bộ ticketTypes state với form field
  useEffect(() => {
    if (formRef.current) {
      formRef.current.setValues({ ticketTypes });
    }
  }, [ticketTypes]);

  // Sync dữ liệu từ product khi component mount
  useEffect(() => {
    if (product) {
      // Sync tags
      setTempTags(product.tags || []);

      // Sync ảnh hiện có với state
      const productWithImages = product as unknown as Record<string, unknown>;
      const images = productWithImages?.['images'] as Array<{ key: string; position: number; url: string }> | undefined;

      if (images && images.length > 0) {
        const existingImages: FileWithMetadata[] = images
          .sort((a, b) => a.position - b.position)
          .map((image, index) => {
            const fileName = image.key.split('/').pop() || `image-${index}.jpg`;
            const placeholderFile = new File([], fileName, {
              type: 'image/jpeg',
              lastModified: Date.now()
            });

            return {
              id: `existing-${image.key}-${index}`,
              file: placeholderFile,
              preview: image.url,
            };
          });

        setMediaFiles(existingImages);
      } else {
        setMediaFiles([]);
      }

      // Sync event data từ advancedInfo
      const advancedInfo = product.advancedInfo as Record<string, unknown>;
      if (advancedInfo) {
        // Sync ticket types từ advancedInfo.ticketTypes
        if (advancedInfo['ticketTypes'] && Array.isArray(advancedInfo['ticketTypes'])) {
          const existingTicketTypes: FormEventTicketType[] = advancedInfo['ticketTypes'].map((ticket: Record<string, unknown>, index: number) => ({
            id: `ticket-${ticket['id'] || index}`,
            name: (ticket['name'] as string) || '',
            price: (ticket['price'] as number) || 0,
            currency: 'VND',
            totalTickets: (ticket['quantity'] as number) || 1,
            minQuantityPerOrder: (ticket['minQuantityPerPurchase'] as number) || 1,
            maxQuantityPerOrder: (ticket['maxQuantityPerPurchase'] as number) || 10,
            saleStartTime: ticket['startTime'] ? new Date(ticket['startTime'] as string | number) : undefined,
            saleEndTime: ticket['endTime'] ? new Date(ticket['endTime'] as string | number) : undefined,
            description: (ticket['description'] as string) || '',
            sku: (ticket['sku'] as string) || '',
          }));
          setTicketTypes(existingTicketTypes);
        } else {
          // Nếu không có ticket types, tạo một ticket mặc định
          setTicketTypes([{
            id: `ticket-${Date.now()}`,
            name: '',
            price: 0,
            currency: 'VND',
            totalTickets: 1,
            minQuantityPerOrder: 1,
            maxQuantityPerOrder: 10,
          }]);
        }
      }
    }
  }, [product]);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(() => {
    const advancedInfo = product.advancedInfo as Record<string, unknown>;

    return {
      name: product.name || '',
      description: product.description || '',
      tags: product.tags || [],
      customFields: [],
      media: [],
      // Event product defaults từ advancedInfo
      eventDateTime: advancedInfo?.['startDate'] ? new Date(advancedInfo['startDate'] as string | number) : undefined,
      eventLocation: (advancedInfo?.['eventLocation'] as string) || '',
      attendanceMode: (advancedInfo?.['eventFormat'] === 'ONLINE' ? 'ONLINE' : 'OFFLINE') as 'ONLINE' | 'OFFLINE',
      zoomLink: (advancedInfo?.['eventLink'] as string) || '',
      ticketTypes: [],
    };
  }, [product]);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Xác định giá trị mặc định dựa trên kiểu dữ liệu
        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';

        // Xác định giá trị mặc định dựa trên type hoặc component
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm loại vé mới
  const handleAddTicketType = useCallback(() => {
    const newTicket: FormEventTicketType = {
      id: `ticket-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      totalTickets: 1,
      minQuantityPerOrder: 1,
      maxQuantityPerOrder: 10,
    };
    setTicketTypes(prev => [...prev, newTicket]);
  }, []);

  // Xóa loại vé
  const handleRemoveTicketType = useCallback((ticketId: string) => {
    setTicketTypes(prev => prev.filter(ticket => ticket.id !== ticketId));
  }, []);

  // Cập nhật thông tin loại vé
  const handleUpdateTicketType = useCallback((ticketId: string, field: keyof FormEventTicketType, value: string | number | Date | null | undefined) => {
    setTicketTypes(prev =>
      prev.map(ticket => {
        if (ticket.id === ticketId) {
          return {
            ...ticket,
            [field]: value,
          };
        }
        return ticket;
      })
    );
  }, []);

  // Xử lý submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 EventProductEditForm handleSubmit called with values:', values);

    // Đồng bộ ticketTypes state với form values trước khi validate
    const formValues = {
      ...values,
      ticketTypes: ticketTypes,
    } as EventProductFormValues;

    if (!formValues.name) {
      console.error('❌ Missing required fields:', {
        name: formValues.name,
      });
      NotificationUtil.error({
        message: t('business:product.form.validation.eventRequiredFieldsMissing'),
        duration: 3000,
      });
      return;
    }

    // Kiểm tra ticket types
    const validTicketTypes = ticketTypes.filter(ticket => ticket.name.trim() !== '');
    if (validTicketTypes.length === 0) {
      NotificationUtil.error({
        message: t('business:product.form.validation.ticketTypesRequired'),
        duration: 3000,
      });
      return;
    }

    try {
      const finalFormValues = formValues as EventProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', finalFormValues);

      const productData: UpdateProductDto = {
        name: finalFormValues.name,
      };

      // Thêm các thuộc tính optional khi có giá trị
      if (finalFormValues.description && finalFormValues.description.trim()) {
        productData.description = finalFormValues.description.trim();
      }

      if (tempTags && tempTags.length > 0) {
        productData.tags = tempTags;
      }

      // Thêm custom fields nếu có
      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Tạo event advanced info
      const advancedInfo = {
        purchaseCount: 0,
        startDate: finalFormValues.eventDateTime ? finalFormValues.eventDateTime.getTime() : Date.now(),
        endDate: finalFormValues.eventDateTime
          ? finalFormValues.eventDateTime.getTime() + (2 * 60 * 60 * 1000) // +2 hours default
          : Date.now() + (2 * 60 * 60 * 1000),
        timezone: 'Asia/Ho_Chi_Minh', // Add required timezone property
        eventLocation: finalFormValues.eventLocation || '',
        eventFormat: finalFormValues.attendanceMode || 'OFFLINE',
        eventLink: finalFormValues.zoomLink || '',
        ticketTypes: validTicketTypes.map((ticket, index) => ({
          id: index + 1,
          name: ticket.name,
          price: ticket.price || 0,
          startTime: ticket.saleStartTime ? ticket.saleStartTime.getTime() : Date.now(),
          endTime: ticket.saleEndTime ? ticket.saleEndTime.getTime() : Date.now() + (30 * 24 * 60 * 60 * 1000), // +30 days
          timezone: 'Asia/Ho_Chi_Minh', // Add required timezone property for ticket types
          description: ticket.description || '',
          quantity: ticket.totalTickets || 1,
          minQuantityPerPurchase: ticket.minQuantityPerOrder || 1,
          maxQuantityPerPurchase: ticket.maxQuantityPerOrder || 10,
          status: 'PENDING' as const, // Add required status property
          sku: ticket.sku || `${ticket.name.toUpperCase().replace(/\s+/g, '-')}-${index + 1}`,
          imagesMediaTypes: ['image/jpeg'],
        })),
      };

      productData.advancedInfo = advancedInfo;

      // Thêm thông tin về ảnh mới nếu có
      const newImagesForRequest = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      if (newImagesForRequest.length > 0) {
        productData.images = newImagesForRequest.map((file, index) => ({
          operation: 'ADD' as const,
          position: index,
          mimeType: file.file.type || 'image/jpeg',
        }));
      }

      console.log('🔍 Final product data to update:', productData);

      // Gọi API cập nhật sản phẩm
      const response = await updateProductMutation.mutateAsync({
        id: productId,
        data: productData,
      });

      console.log('✅ Product updated successfully:', response);

      // Upload media mới nếu có (chỉ upload ảnh mới, không upload lại ảnh hiện có)
      const newMediaFiles = mediaFiles.filter(file => !file.id.startsWith('existing-'));

      if (newMediaFiles.length > 0) {
        try {
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls && response.uploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              const uploadTasks = newMediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              NotificationUtil.success({
                message: t('business:product.mediaUploadSuccess', 'Tải lên ảnh sản phẩm thành công'),
                duration: 3000,
              });
            }
          }
        } catch (uploadError) {
          console.error('❌ Error uploading product images:', uploadError);
          NotificationUtil.warning({
            message: t('business:product.mediaUploadError', 'Có lỗi xảy ra khi tải lên ảnh sản phẩm'),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);

      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sự kiện thành công'),
        duration: 3000,
      });

      // Quay lại trang trước
      onCancel();
    } catch (error) {
      console.error('Error in EventProductEditForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sự kiện'),
        duration: 3000,
      });
    }
  };

  return (
    <FormMultiWrapper title={t('business:product.form.editEventTitle', 'Chỉnh sửa sự kiện')}>
      <Form
        ref={formRef}
        schema={eventProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || t('business:product.form.validation.formValidationError');
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder={t('business:product.form.eventNamePlaceholder')} />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('business:product.form.eventDescriptionPlaceholder')}
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder={t('business:product.form.tagsPlaceholder')}
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Thông tin tổ chức sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.eventInfo', '2. Thông tin tổ chức sự kiện')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Thời gian sự kiện */}
            <FormItem name="eventDateTime" label={t('business:product.form.eventProduct.eventDateTime')} required>
              <Controller
                name="eventDateTime"
                render={({ field }) => (
                  <DateTimePicker
                    value={field.value || null}
                    onChange={(date) => field.onChange(date)}
                    placeholder={t('business:product.form.eventProduct.eventDateTimePlaceholder')}
                    format="dd/MM/yyyy HH:mm"
                    timeFormat="24h"
                    fullWidth
                    minDate={new Date()}
                  />
                )}
              />
            </FormItem>

            {/* Hình thức tham dự */}
            <FormItem name="attendanceMode" label={t('business:product.form.eventProduct.attendanceMode.title')} required>
              <Select
                fullWidth
                options={[
                  { value: 'OFFLINE', label: t('business:product.form.eventProduct.attendanceMode.offline') },
                  { value: 'ONLINE', label: t('business:product.form.eventProduct.attendanceMode.online') },
                ]}
              />
            </FormItem>

            {/* Địa điểm sự kiện - chỉ hiển thị cho offline */}
            <ConditionalField
              condition={{
                field: 'attendanceMode',
                type: ConditionType.EQUALS,
                value: 'OFFLINE',
              }}
            >
              <FormItem name="eventLocation" label={t('business:product.form.eventProduct.eventLocation')} required>
                <Input fullWidth placeholder={t('business:product.form.eventProduct.eventLocationPlaceholder')} />
              </FormItem>
            </ConditionalField>

            {/* Link Zoom - chỉ hiển thị cho online */}
            <ConditionalField
              condition={{
                field: 'attendanceMode',
                type: ConditionType.EQUALS,
                value: 'ONLINE',
              }}
            >
              <FormItem name="zoomLink" label={t('business:product.form.eventProduct.zoomLink')}>
                <Input fullWidth placeholder={t('business:product.form.eventProduct.zoomLinkPlaceholder')} />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 3. Loại vé sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.ticketTypes', '3. Loại vé sự kiện')} ({ticketTypes.length})
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Danh sách loại vé */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.eventProduct.ticketTypes.title')}
                </Typography>
                <IconCard
                  icon="plus"
                  title={t('business:product.form.eventProduct.ticketTypes.addTicketType')}
                  onClick={handleAddTicketType}
                  variant="secondary"
                  size="sm"
                  className="cursor-pointer"
                />
              </div>

              {ticketTypes.length === 0 && (
                <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
                    {t('business:product.form.eventProduct.ticketTypes.noTicketTypes')}
                  </Typography>
                  <IconCard
                    icon="plus"
                    title={t('business:product.form.eventProduct.ticketTypes.addFirstTicketType')}
                    onClick={handleAddTicketType}
                    variant="primary"
                    size="sm"
                    className="cursor-pointer mt-2"
                  />
                </div>
              )}

              {ticketTypes.map((ticket, index) => (
                <CollapsibleCard
                  key={ticket.id}
                  title={
                    <div className="flex justify-between items-center w-full">
                      <div className="flex items-center space-x-4">
                        <Typography variant="body2" className="font-medium">
                          {ticket.name || `${t('business:product.form.eventProduct.ticketTypes.defaultTicketName')} ${index + 1}`}
                        </Typography>
                        <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                          {ticket.price > 0 ? `${ticket.price.toLocaleString()} ${ticket.currency}` : '0 VND'}
                        </Typography>
                        <Typography variant="body2" className="text-gray-500 dark:text-gray-500">
                          {ticket.totalTickets} vé
                        </Typography>
                        {ticket.id && (
                          <Typography variant="body2" className="text-gray-400 dark:text-gray-600 text-xs">
                            ID: {ticket.id.split('-').pop()}
                          </Typography>
                        )}
                      </div>
                      <IconCard
                        icon="trash"
                        title={t('business:product.form.eventProduct.ticketTypes.removeTicketType')}
                        onClick={() => handleRemoveTicketType(ticket.id)}
                        variant="danger"
                        size="sm"
                        className="cursor-pointer"
                      />
                    </div>
                  }
                  defaultOpen={false}
                >
                  <div className="space-y-4">
                    {/* Tên loại vé và giá */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.ticketName')} required>
                        <Input
                          fullWidth
                          placeholder={t('business:product.form.eventProduct.ticketTypes.ticketNamePlaceholder')}
                          value={ticket.name}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'name', e.target.value)}
                        />
                      </FormItem>
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.price')} required>
                        <Input
                          fullWidth
                          type="number"
                          min="0"
                          placeholder="0"
                          value={ticket.price}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'price', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.currency')} required>
                        <Select
                          fullWidth
                          value={ticket.currency || 'VND'}
                          onChange={(value) => {
                            if (typeof value === 'string') {
                              handleUpdateTicketType(ticket.id, 'currency', value);
                            }
                          }}
                          options={[
                            { value: 'VND', label: 'VND' },
                            { value: 'USD', label: 'USD' },
                            { value: 'EUR', label: 'EUR' },
                          ]}
                        />
                      </FormItem>
                    </div>

                    {/* Tổng số vé và SKU */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.totalTickets')} required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="100"
                          value={ticket.totalTickets}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'totalTickets', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.sku')}>
                        <Input
                          fullWidth
                          placeholder={t('business:product.form.eventProduct.ticketTypes.skuPlaceholder')}
                          value={ticket.sku || ''}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'sku', e.target.value)}
                        />
                      </FormItem>
                    </div>

                    {/* Thời gian bán vé */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.saleStartTime')}>
                        <DateTimePicker
                          value={ticket.saleStartTime || null}
                          onChange={(date) => handleUpdateTicketType(ticket.id, 'saleStartTime', date)}
                          placeholder={t('business:product.form.eventProduct.ticketTypes.saleStartTimePlaceholder')}
                          format="dd/MM/yyyy HH:mm"
                          timeFormat="24h"
                          fullWidth
                          minDate={new Date()}
                        />
                      </FormItem>
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.saleEndTime')}>
                        <DateTimePicker
                          value={ticket.saleEndTime || null}
                          onChange={(date) => handleUpdateTicketType(ticket.id, 'saleEndTime', date)}
                          placeholder={t('business:product.form.eventProduct.ticketTypes.saleEndTimePlaceholder')}
                          format="dd/MM/yyyy HH:mm"
                          timeFormat="24h"
                          fullWidth
                          minDate={ticket.saleStartTime || new Date()}
                        />
                      </FormItem>
                    </div>

                    {/* Số lượng mua tối thiểu/tối đa */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.minQuantityPerOrder')} required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="1"
                          value={ticket.minQuantityPerOrder}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'minQuantityPerOrder', Number(e.target.value))}
                        />
                      </FormItem>
                      <FormItem label={t('business:product.form.eventProduct.ticketTypes.maxQuantityPerOrder')} required>
                        <Input
                          fullWidth
                          type="number"
                          min="1"
                          placeholder="10"
                          value={ticket.maxQuantityPerOrder}
                          onChange={(e) => handleUpdateTicketType(ticket.id, 'maxQuantityPerOrder', Number(e.target.value))}
                        />
                      </FormItem>
                    </div>

                    {/* Hình ảnh vé */}
                    <FormItem label={t('business:product.form.eventProduct.ticketTypes.ticketImage')}>
                      <AvatarImageUploader
                        value={ticket.ticketImage || ''}
                        onChange={(imageUrl) => handleUpdateTicketType(ticket.id, 'ticketImage', imageUrl)}
                        placeholder={t('business:product.form.eventProduct.ticketTypes.ticketImagePlaceholder')}
                        size="lg"
                        shape="square"
                      />
                    </FormItem>

                    {/* Mô tả loại vé */}
                    <FormItem label={t('business:product.form.eventProduct.ticketTypes.description')}>
                      <Textarea
                        fullWidth
                        rows={3}
                        placeholder={t('business:product.form.eventProduct.ticketTypes.descriptionPlaceholder')}
                        value={ticket.description || ''}
                        onChange={(e) => handleUpdateTicketType(ticket.id, 'description', e.target.value)}
                      />
                    </FormItem>
                  </div>
                </CollapsibleCard>
              ))}
            </div>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh sự kiện */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh sự kiện')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '5. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => {
                  const fieldValue = field.value?.['value'] as string | number | boolean || '';

                  // Debug logging
                  console.log(`🔍 EventProductEdit CustomField ${field.label}:`, {
                    fieldId: field.fieldId,
                    type: field.type,
                    component: field.component,
                    rawValue: field.value,
                    extractedValue: fieldValue,
                    valueType: typeof fieldValue
                  });

                  return (
                    <CustomFieldRenderer
                      key={field.id}
                      field={field}
                      value={fieldValue}
                      onChange={(value: string | number | boolean) => {
                        console.log(`🔄 EventProductEdit CustomField ${field.label} onChange:`, {
                          oldValue: fieldValue,
                          newValue: value,
                          newValueType: typeof value
                        });
                        handleUpdateCustomFieldInProduct(field.id, value);
                      }}
                      onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                    />
                  );
                })}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="save"
            title={
              updateProductMutation.isPending || isUploading
                ? t('business:product.form.updating', 'Đang cập nhật...')
                : t('business:product.form.update', 'Cập nhật sự kiện')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={updateProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default EventProductEditForm;
