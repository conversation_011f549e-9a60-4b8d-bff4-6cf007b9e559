import React, { useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Table, Icon, ActionMenu, CollapsibleCard, Loading } from '@/shared/components/common';
import { CustomerDetailData } from './types';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useCustomerOrders } from '../../../hooks/useOrderQuery';
import { OrderQueryParams, OrderStatus } from '../../../services/order.service';

interface CustomerOrdersProps {
  customer: CustomerDetailData;
}

// Interface cho dữ liệu order từ API customer orders
interface CustomerOrderItem {
  id: string;
  billInfo: {
    tax?: number;
    total: number;
    discount?: number;
    subtotal?: number;
    codAmount?: number;
    shippingFee?: number;
    paymentMethod?: string;
    paymentStatus?: string;
    status?: string;
    payment_method?: string;
  };
  shippingStatus: string;
  createdAt: string;
  source: string;
  orderStatus: string;
}

/**
 * Component hiển thị đơn hàng của khách hàng
 */
const CustomerOrders: React.FC<CustomerOrdersProps> = ({ customer }) => {
  const { t } = useTranslation(['business', 'common']);

  // Format currency
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  }, []);

  // Format date
  const formatDate = useCallback((dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  }, []);

  // Helper functions for status translation
  const getOrderStatusText = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return t('business:order.status.pending');
      case 'processing':
        return t('business:order.status.processing');
      case 'confirmed':
        return t('business:order.status.confirmed');
      case 'shipped':
        return t('business:order.status.shipped');
      case 'delivered':
        return t('business:order.status.delivered');
      case 'cancelled':
        return t('business:order.status.cancelled');
      case 'completed':
        return t('business:order.status.completed');
      default:
        return status;
    }
  }, [t]);

  const getPaymentStatusText = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return t('business:order.paymentStatus.paid');
      case 'pending':
        return t('business:order.paymentStatus.pending');
      case 'unpaid':
        return t('business:order.paymentStatus.unpaid');
      case 'failed':
        return t('business:order.paymentStatus.failed');
      case 'partially_paid':
        return t('business:order.paymentStatus.partiallyPaid');
      default:
        return status;
    }
  }, [t]);

  const getShippingStatusText = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return t('business:order.shippingStatus.pending');
      case 'processing':
        return t('business:order.shippingStatus.processing');
      case 'shipped':
        return t('business:order.shippingStatus.shipped');
      case 'delivered':
        return t('business:order.shippingStatus.delivered');
      case 'cancelled':
        return t('business:order.shippingStatus.cancelled');
      case 'sorting':
        return t('business:order.shippingStatus.sorting');
      default:
        return status;
    }
  }, [t]);

  // Table columns
  const columns: TableColumn<CustomerOrderItem>[] = useMemo(() => [
    {
      key: 'id',
      title: t('business:customer.detail.orderCode'),
      dataIndex: 'id',
      render: (value: unknown) => (
        <div className="flex items-center space-x-2">
          <Icon name="shopping-cart" size="sm" className="text-muted" />
          <Typography variant="body2" className="text-foreground font-medium">
            #{String(value)}
          </Typography>
        </div>
      ),
    },
    {
      key: 'createdAt',
      title: t('business:customer.detail.orderDate'),
      dataIndex: 'createdAt',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-foreground">
          {formatDate(new Date(Number(value)).toISOString())}
        </Typography>
      ),
    },
    {
      key: 'orderStatus',
      title: t('business:customer.detail.orderStatus'),
      dataIndex: 'orderStatus',
      render: (value: unknown) => {
        const statusValue = String(value);
        return (
          <Typography variant="body2" className="text-foreground">
            {getOrderStatusText(statusValue)}
          </Typography>
        );
      },
    },
    {
      key: 'billInfo',
      title: t('business:customer.detail.paymentStatus'),
      dataIndex: 'billInfo',
      render: (value: unknown) => {
        const billInfo = value as CustomerOrderItem['billInfo'];
        const paymentStatus = billInfo?.paymentStatus || billInfo?.status || 'unknown';
        return (
          <Typography variant="body2" className="text-foreground">
            {getPaymentStatusText(paymentStatus)}
          </Typography>
        );
      },
    },
    {
      key: 'billInfo',
      title: t('business:customer.detail.totalAmount'),
      dataIndex: 'billInfo',
      render: (value: unknown) => {
        const billInfo = value as CustomerOrderItem['billInfo'];
        return (
          <Typography variant="body2" className="text-foreground font-medium">
            {formatCurrency(billInfo?.total || 0)}
          </Typography>
        );
      },
    },
    {
      key: 'shippingStatus',
      title: t('business:customer.detail.shippingStatus'),
      dataIndex: 'shippingStatus',
      render: (value: unknown) => {
        const shippingStatus = String(value);
        return (
          <Typography variant="body2" className="text-foreground">
            {getShippingStatusText(shippingStatus)}
          </Typography>
        );
      },
    },
    {
      key: 'source',
      title: t('business:customer.detail.source'),
      dataIndex: 'source',
      render: (value: unknown) => (
        <Typography variant="body2" className="text-muted">
          {String(value)}
        </Typography>
      ),
    },
    {
      key: 'actions',
      title: t('common:actions'),
      render: (_, record: CustomerOrderItem) => (
        <ActionMenu
          items={[
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleViewOrder(record.id),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEditOrder(record.id),
            },
            {
              id: 'divider1',
              divider: true,
            },
            {
              id: 'cancel',
              label: t('business:customer.order.cancel'),
              icon: 'x',
              onClick: () => handleCancelOrder(record.id),
              disabled: record.orderStatus === 'completed' || record.orderStatus === 'cancelled',
            },
          ]}
        />
      ),
    },
  ], [t, formatCurrency, formatDate, getOrderStatusText, getPaymentStatusText, getShippingStatusText]);

  // Action handlers
  const handleViewOrder = (orderId: string) => {
    console.log('View order:', orderId);
    // Implement view order logic
  };

  const handleEditOrder = (orderId: string) => {
    console.log('Edit order:', orderId);
    // Implement edit order logic
  };

  const handleCancelOrder = (orderId: string) => {
    console.log('Cancel order:', orderId);
    // Implement cancel order logic
  };

  // Create query params function
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
  }): OrderQueryParams => {
    const queryParams: OrderQueryParams = {
      page: params.page,
      limit: params.pageSize,
    };

    // Chỉ thêm các property khi có giá trị thực sự
    if (params.searchTerm) {
      queryParams.search = params.searchTerm;
    }
    if (params.sortBy) {
      queryParams.sortBy = params.sortBy;
    }
    if (params.sortDirection) {
      queryParams.sortDirection = params.sortDirection;
    }
    if (params.filterValue && params.filterValue !== 'all') {
      queryParams.status = String(params.filterValue) as OrderStatus;
    }

    return queryParams;
  };

  // Filter options for order status
  const filterOptions = [
    { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
    { id: 'pending', label: t('business:order.status.pending'), icon: 'clock', value: 'pending' },
    { id: 'processing', label: t('business:order.status.processing'), icon: 'loader', value: 'processing' },
    { id: 'confirmed', label: t('business:order.status.confirmed'), icon: 'check', value: 'confirmed' },
    { id: 'shipped', label: t('business:order.status.shipped'), icon: 'truck', value: 'shipped' },
    { id: 'delivered', label: t('business:order.status.delivered'), icon: 'check-circle', value: 'delivered' },
    { id: 'cancelled', label: t('business:order.status.cancelled'), icon: 'x-circle', value: 'cancelled' },
    { id: 'completed', label: t('business:order.status.completed'), icon: 'check-circle-2', value: 'completed' },
  ];

  // Use data table hook
  const dataTable = useDataTable(
    useDataTableConfig<CustomerOrderItem, OrderQueryParams>({
      columns,
      filterOptions,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách đơn hàng của khách hàng
  const { data: ordersResponse, isLoading } = useCustomerOrders(
    customer.id,
    dataTable.queryParams
  );

  const ordersData = ordersResponse || {
    items: [],
    meta: {
      currentPage: 1,
      totalItems: 0,
      totalPages: 1,
    },
  };

  return (
    <CollapsibleCard
      title={
        <div className="flex items-center justify-between w-full">
          <Typography variant="h6" className="text-foreground">
            {t('business:customer.detail.orders')}
          </Typography>
          <Typography variant="body2" className="text-muted">
            {isLoading ? (
              <Loading size="sm" />
            ) : (
              `${ordersData.meta.totalItems} ${t('business:customer.detail.totalOrders').toLowerCase()}`
            )}
          </Typography>
        </div>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* Menu Icon Bar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
          columns={dataTable.columnVisibility.visibleColumns}
          showDateFilter={false}
          showColumnFilter={true}
        />

        {/* Table */}
        <Table<CustomerOrderItem>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={ordersData.items as CustomerOrderItem[]}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: ordersData.meta.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: ordersData.meta.totalItems,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </div>
    </CollapsibleCard>
  );
};

export default CustomerOrders;
