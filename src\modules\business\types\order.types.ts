/**
 * <PERSON><PERSON><PERSON> nghĩ<PERSON> cá<PERSON> types cho Order trong module business
 */

export * from '../services/order.service';

/**
 * Enum cho trạng thái đơn hàng (đồng bộ với backend)
 */
export enum OrderStatusEnum {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

/**
 * Enum cho trạng thái vận chuyển (đồng bộ với backend)
 */
export enum ShippingStatusEnum {
  PENDING = 'pending',
  PREPARING = 'preparing',
  SHIPPED = 'shipped',
  IN_TRANSIT = 'in_transit',
  SORTING = 'sorting',
  DELIVERED = 'delivered',
  DELIVERY_FAILED = 'delivery_failed',
  RETURNING = 'returning',
  CANCELLED = 'cancelled'
}

/**
 * Enum cho trạng thái thanh toán (đồng bộ với backend)
 */
export enum PaymentStatusEnum {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

/**
 * Enum cho phương thức thanh toán (đồng bộ với backend)
 */
export enum PaymentMethodEnum {
  CASH = 'CASH',
  BANKING = 'BANKING',
  CREDIT_CARD = 'CREDIT_CARD',
  E_WALLET = 'E_WALLET'
}

/**
 * Interface cho địa chỉ
 */
export interface AddressDto {
  province: string;
  district: string;
  ward: string;
  address: string;
  provinceId?: number;
  districtId?: number;
  wardCode?: string;
}

/**
 * Interface cho thông tin khách hàng trong đơn hàng
 */
export interface OrderCustomerDto {
  id?: number;
  name: string;
  email: string;
  phone: string;
  address: AddressDto;
}

/**
 * Interface cho sản phẩm trong đơn hàng (enhanced)
 */
export interface OrderItemDto {
  productId: number;
  variantId?: number;
  productName: string;
  variantName?: string;
  productType?: import('./product.types').ProductTypeEnum;
  quantity: number;
  price: number;
  totalPrice: number;
  shipmentConfig?: {
    lengthCm?: number;
    widthCm?: number;
    heightCm?: number;
    weightGram?: number;
  };
  image?: string;
  sku?: string;
}

/**
 * Enum cho phương thức vận chuyển
 */
export enum ShippingMethod {
  GHN = 'GHN',
  GHTK = 'GHTK',
  SELF = 'SELF',
}

/**
 * Enum cho phương thức giao hàng sản phẩm số
 */
export enum DigitalDeliveryMethod {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  ZALO = 'ZALO',
  TELEGRAM = 'TELEGRAM',
  WHATSAPP = 'WHATSAPP',
  DOWNLOAD_LINK = 'DOWNLOAD_LINK',
}

/**
 * Interface cho thông tin giao hàng sản phẩm số
 */
export interface DigitalDeliveryDto {
  method: DigitalDeliveryMethod;
  recipient: string; // email, phone number, username, etc.
  message?: string;
  scheduledDelivery?: string;
  deliveryStatus?: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
}

/**
 * Interface cho thông tin vận chuyển
 */
export interface ShippingDto {
  method: ShippingMethod;
  serviceId?: number;
  serviceName?: string;
  fromAddress: AddressDto;
  toAddress: AddressDto;
  fee: number;
  estimatedDelivery?: string;
  note?: string;
  trackingCode?: string;
  addressId?: number; // ID của địa chỉ giao hàng đã chọn
}

/**
 * Enum cho trạng thái vận chuyển (legacy - sử dụng ShippingStatusEnum thay thế)
 * @deprecated Use ShippingStatusEnum instead
 */
export enum ShippingStatus {
  PENDING = 'PENDING',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  RETURNED = 'RETURNED',
}

/**
 * Interface cho tạo đơn hàng mới (enhanced)
 */
export interface CreateEnhancedOrderDto {
  customer: OrderCustomerDto;
  items: OrderItemDto[];
  shipping: ShippingDto | undefined;
  digitalDelivery: DigitalDeliveryDto | undefined;
  payment: {
    method: string;
    status: string;
    codAmount?: number;
  };
  notes: string | undefined;
  tags: string[] | undefined;
}

/**
 * Interface cho đơn hàng (enhanced)
 */
export interface EnhancedOrderDto {
  id: number;
  orderNumber: string;
  customer: OrderCustomerDto;
  items: OrderItemDto[];
  subtotal: number;
  shippingFee: number;
  tax?: number;
  totalAmount: number;
  status: string;
  paymentMethod: string;
  paymentStatus: string;
  shipping: ShippingDto;
  shippingStatus: ShippingStatus;
  notes?: string;
  tags?: string[];
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface cho tham số lọc đơn hàng (enhanced)
 */
export interface EnhancedOrderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  paymentStatus?: string;
  shippingStatus?: ShippingStatus;
  shippingMethod?: ShippingMethod;
  customerId?: number;
  fromDate?: string;
  toDate?: string;
  minAmount?: number;
  maxAmount?: number;
  tags?: string[];
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho thống kê đơn hàng
 */
export interface OrderStatsDto {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  pendingOrders: number;
  processingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalShippingFee: number;
}
