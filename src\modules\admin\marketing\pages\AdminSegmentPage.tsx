import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  ActionMenu,
  ConfirmDeleteModal,
} from '@/shared/components/common';
import type { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SegmentForm, { SegmentFormValues } from '@/modules/marketing/components/forms/SegmentForm';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  SegmentStatus,
  SegmentQueryParams,
  Segment,
  CreateSegmentRequest,
  UpdateSegmentRequest,
} from '@/modules/marketing/types/segment.types';
import {
  useSegments,
  useCreateSegment,
  useUpdateSegment,
  useDeleteSegment,
  useBulkDeleteSegments,
  useSegment,
} from '@/modules/marketing/hooks/useSegmentQuery';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang quản lý phân đoạn cho Admin
 */
const AdminSegmentPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho chọn nhiều và xóa nhiều
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho chỉnh sửa
  const [editingSegmentId, setEditingSegmentId] = useState<number | null>(null);
  const [showEditForm, setShowEditForm] = useState(false);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Hooks để gọi API
  const { mutateAsync: createSegment } = useCreateSegment();
  const { mutateAsync: bulkDeleteSegments } = useBulkDeleteSegments();
  const { mutateAsync: updateSegment } = useUpdateSegment(editingSegmentId ?? 1);
  const { mutateAsync: deleteSegment } = useDeleteSegment();

  // Hook để lấy chi tiết segment khi edit
  const { data: editingSegment, isLoading: isLoadingSegmentDetail } = useSegment(editingSegmentId || 0);

  // Xử lý chỉnh sửa
  const handleEdit = useCallback((segment: Segment) => {
    setEditingSegmentId(segment.id);
    setShowEditForm(true);
  }, []);

  // Cấu hình columns cho table
  const columns = useMemo(() => [
    {
      title: t('marketing:segment.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sortable: true,
    },
    {
      title: t('marketing:segment.name', 'Tên phân đoạn'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
    },
    {
      title: t('marketing:segment.description', 'Mô tả'),
      dataIndex: 'description',
      key: 'description',
      render: (value: unknown) => (value as string) || '-',
    },
    {
      title: t('marketing:segment.status', 'Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      render: (value: unknown) => {
        const status = value as SegmentStatus;
        const statusConfig = {
          [SegmentStatus.DRAFT]: { label: 'Nháp', color: 'text-yellow-600' },
          [SegmentStatus.ACTIVE]: { label: 'Hoạt động', color: 'text-green-600' },
          [SegmentStatus.INACTIVE]: { label: 'Không hoạt động', color: 'text-gray-600' },
        };
        const config = statusConfig[status] || statusConfig[SegmentStatus.DRAFT];
        return <span className={config.color}>{config.label}</span>;
      },
    },
    {
      title: t('marketing:segment.totalContacts', 'Số liên hệ'),
      dataIndex: 'totalContacts',
      key: 'totalContacts',
      width: 120,
      sortable: true,
      render: (value: unknown) => (value as number)?.toLocaleString() || '0',
    },
    {
      title: t('marketing:segment.audienceName', 'Audience'),
      dataIndex: 'audienceName',
      key: 'audienceName',
    },
  ], [t]);

  // Cấu hình data table
  const dataTable = useDataTable(
    useDataTableConfig<Segment, SegmentQueryParams>({
      columns,
      createQueryParams: (params) => {
        const queryParams: SegmentQueryParams = {
          page: params.page,
          limit: params.pageSize,
        };

        if (params.searchTerm) {
          queryParams.search = params.searchTerm;
        }

        if (params.sortBy) {
          queryParams.sortBy = params.sortBy;
        }

        if (params.sortDirection) {
          queryParams.sortDirection = params.sortDirection === 'ASC' ? 'ASC' : 'DESC';
        }

        return queryParams;
      },
    })
  );

  const { data, isLoading, refetch } = useSegments(dataTable.queryParams);

  // Xử lý xóa đơn lẻ
  const handleDelete = useCallback(async (id: number) => {
    try {
      await deleteSegment(id);
      refetch();
      NotificationUtil.success({
        message: t('marketing:segment.deleteSuccess', 'Đã xóa phân đoạn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Delete error:', error);
      NotificationUtil.error({
        message: t('marketing:segment.deleteError', 'Có lỗi xảy ra khi xóa phân đoạn'),
        duration: 3000,
      });
    }
  }, [deleteSegment, refetch, t]);

  // Thêm actions column với handleDelete
  const columnsWithActions = useMemo(() => [
    ...columns,
    {
      title: t('common:actions', 'Thao tác'),
      key: 'actions',
      width: 120,
      render: (_: unknown, record: unknown) => {
        const segment = record as Segment;
        const actionItems: ActionMenuItem[] = [
          {
            id: 'edit',
            icon: 'edit',
            label: t('common:edit', 'Chỉnh sửa'),
            onClick: () => handleEdit(segment),
          },
          {
            id: 'delete',
            icon: 'trash',
            label: t('common:delete', 'Xóa'),
            onClick: () => handleDelete(segment.id),
          },
        ];

        return <ActionMenu items={actionItems} />;
      },
    },
  ], [columns, t, handleEdit, handleDelete]);

  // Cập nhật table data khi có data mới
  useEffect(() => {
    if (data) {
      dataTable.updateTableData(data, isLoading);
    }
  }, [data, isLoading, dataTable]);

  // Xử lý chọn nhiều
  const handleSelectionChange = useCallback((selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  }, []);

  // Xử lý xóa nhiều
  const handleBulkDelete = useCallback(async () => {
    try {
      const ids = selectedRowKeys.map(key => Number(key));
      await bulkDeleteSegments(ids);
      setSelectedRowKeys([]);
      setShowBulkDeleteConfirm(false);
      refetch();
      NotificationUtil.success({
        message: t('marketing:segment.bulkDeleteSuccess', 'Đã xóa các phân đoạn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Bulk delete error:', error);
      NotificationUtil.error({
        message: t('marketing:segment.bulkDeleteError', 'Có lỗi xảy ra khi xóa phân đoạn'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, bulkDeleteSegments, refetch, t]);

  // Xử lý submit form tạo segment mới
  const handleSubmitCreateSegment = async (values: SegmentFormValues) => {
    try {
      console.log('Form values:', values);

      // Chuẩn bị dữ liệu theo định dạng yêu cầu của API
      // API yêu cầu name, description (optional), và criteria với groups
      const segmentData: CreateSegmentRequest = {
        name: values.name,
        ...(values.description && { description: values.description }),
        criteria: {
          groups: values.groups.map(group => ({
            logicalOperator: group.logicalOperator,
            conditions: group.conditions.map(condition => ({
              field: condition.field,
              operator: condition.operator,
              value: condition.value,
            })),
          })),
        },
      };

      // Tạo segment
      await createSegment(segmentData);

      // Đóng form
      hideForm();

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:segment.createSuccess', 'Đã tạo phân đoạn thành công'),
        duration: 3000,
      });

      // Refresh data
      refetch();
    } catch (error) {
      console.error('Create segment error:', error);
      NotificationUtil.error({
        message: t('marketing:segment.createError', 'Có lỗi xảy ra khi tạo phân đoạn'),
        duration: 3000,
      });
    }
  };

  // Xử lý submit form cập nhật segment
  const handleSubmitUpdateSegment = async (values: SegmentFormValues) => {
    if (!editingSegmentId) return;

    try {
      // Gửi name, description, và criteria với groups
      const updateData: UpdateSegmentRequest = {
        name: values.name,
        ...(values.description !== undefined && { description: values.description }),
        criteria: {
          groups: values.groups.map(group => ({
            logicalOperator: group.logicalOperator,
            conditions: group.conditions.map(condition => ({
              field: condition.field,
              operator: condition.operator,
              value: condition.value,
            })),
          })),
        },
      };

      await updateSegment(updateData);

      setShowEditForm(false);
      setEditingSegmentId(null);

      NotificationUtil.success({
        message: t('marketing:segment.updateSuccess', 'Đã cập nhật phân đoạn thành công'),
        duration: 3000,
      });

      refetch();
    } catch (error) {
      console.error('Update segment error:', error);
      NotificationUtil.error({
        message: t('marketing:segment.updateError', 'Có lỗi xảy ra khi cập nhật phân đoạn'),
        duration: 3000,
      });
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header với actions */}
      <div className="mb-6">
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={showForm}
          items={[]}
          additionalIcons={[
            {
              icon: 'trash',
              tooltip: t('marketing:segment.bulkDelete', 'Xóa đã chọn'),
              variant: 'primary',
              onClick: () => setShowBulkDeleteConfirm(true),
              className: 'text-red-500',
              condition: selectedRowKeys.length > 0,
            },
          ]}
        />
      </div>

      {/* Table */}
      <Card>
        <Table<Segment>
          columns={columnsWithActions}
          data={data?.items || []}
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: data?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: data?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Form tạo segment mới */}
      <SlideInForm isVisible={isVisible}>
        <SegmentForm
          onSubmit={handleSubmitCreateSegment}
          onCancel={hideForm}
        />
      </SlideInForm>

      {/* Form chỉnh sửa segment */}
      <SlideInForm isVisible={showEditForm && !!editingSegment && !isLoadingSegmentDetail}>
        <SegmentForm
          initialData={editingSegment}
          onSubmit={handleSubmitUpdateSegment}
          onCancel={() => {
            setShowEditForm(false);
            setEditingSegmentId(null);
          }}
        />
      </SlideInForm>

      {/* Confirm delete modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={() => setShowBulkDeleteConfirm(false)}
        onConfirm={handleBulkDelete}
        title={t('marketing:segment.confirmBulkDelete', 'Xác nhận xóa phân đoạn')}
        message={t('marketing:segment.confirmBulkDeleteMessage', {
          count: selectedRowKeys.length,
          defaultValue: `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} phân đoạn đã chọn?`,
        })}
      />
    </div>
  );
};

export default AdminSegmentPage;
