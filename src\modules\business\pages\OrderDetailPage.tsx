import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Icon,
  Loading,
} from '@/shared/components/common';
import { useOrder } from '../hooks/useOrderQuery';
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';
import { OrderStatusEnum, ShippingStatusEnum, PaymentStatusEnum } from '../types/order.types';
import { ApiUserOrder } from '../services/business-api.service';
import { t } from 'i18next';

// Interfaces for order detail data
interface OrderProduct {
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description?: string;
}

interface OrderProductInfo {
  products?: OrderProduct[];
}

/**
 * Interface cho sản phẩm trong đơn hàng
 */
interface OrderProduct {
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description?: string;
}

/**
 * Interface cho thông tin sản phẩm trong đơn hàng
 */
interface OrderProductInfo {
  products?: OrderProduct[];
}

/**
 * Interface cho thông tin logistics
 */
interface OrderLogisticInfo {
  recipientName?: string;
  recipientPhone?: string;
  deliveryAddress?: string;
  carrier?: string;
  trackingNumber?: string;
  estimatedDeliveryTime?: number;
  shippingNote?: string;
}

/**
 * Trang chi tiết đơn hàng
 */
const OrderDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const orderId = id ? parseInt(id) : 0;

  // Lấy chi tiết đơn hàng
  const { data: orderResponse, isLoading, error } = useOrder(orderId);
  const order = orderResponse?.result as unknown as ApiUserOrder;

  console.log('🔍 [OrderDetailPage] Order ID:', orderId);
  console.log('🔍 [OrderDetailPage] Order response:', orderResponse);
  console.log('🔍 [OrderDetailPage] Order data:', order);
  console.log('🔍 [OrderDetailPage] Loading:', isLoading);
  console.log('🔍 [OrderDetailPage] Error:', error);

  // Xử lý loading
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loading size="lg" />
          <p className="mt-4 text-gray-600">{t('business:order.loading')}</p>
        </div>
      </div>
    );
  }

  // Xử lý lỗi
  if (error || !order) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center border border-gray-200">
          <div className="w-16 h-16 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <Icon name="alert-circle" size="lg" className="text-red-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-3">
            {t('business:order.notFound')}
          </h2>
          <p className="text-gray-600 mb-8 leading-relaxed">
            {t('business:order.notFoundDescription')}
          </p>
          <button
            onClick={() => navigate('/business/order')}
            className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            {t('business:order.backToList')}
          </button>
        </div>
      </div>
    );
  }

  // Helper functions để render status
  const getOrderStatusLabel = (status: OrderStatusEnum) => {
    const statusMap: Record<OrderStatusEnum, string> = {
      [OrderStatusEnum.PENDING]: t('business:order.status.pending'),
      [OrderStatusEnum.CONFIRMED]: t('business:order.status.confirmed'),
      [OrderStatusEnum.PROCESSING]: t('business:order.status.processing'),
      [OrderStatusEnum.COMPLETED]: t('business:order.status.completed'),
      [OrderStatusEnum.CANCELLED]: t('business:order.status.cancelled'),
    };
    return statusMap[status] || status;
  };

  const getShippingStatusLabel = (status: ShippingStatusEnum) => {
    const statusMap: Record<ShippingStatusEnum, string> = {
      [ShippingStatusEnum.PENDING]: t('business:order.shippingStatus.pending'),
      [ShippingStatusEnum.PREPARING]: t('business:order.shippingStatus.preparing'),
      [ShippingStatusEnum.SHIPPED]: t('business:order.shippingStatus.shipped'),
      [ShippingStatusEnum.IN_TRANSIT]: t('business:order.shippingStatus.inTransit'),
      [ShippingStatusEnum.SORTING]: t('business:order.shippingStatus.sorting'),
      [ShippingStatusEnum.DELIVERED]: t('business:order.shippingStatus.delivered'),
      [ShippingStatusEnum.DELIVERY_FAILED]: t('business:order.shippingStatus.deliveryFailed'),
      [ShippingStatusEnum.RETURNING]: t('business:order.shippingStatus.returning'),
      [ShippingStatusEnum.CANCELLED]: t('business:order.shippingStatus.cancelled'),
    };
    return statusMap[status] || status;
  };

  const getPaymentStatusLabel = (status: PaymentStatusEnum) => {
    const statusMap: Record<PaymentStatusEnum, string> = {
      [PaymentStatusEnum.PENDING]: t('business:order.paymentStatus.pending'),
      [PaymentStatusEnum.PAID]: t('business:order.paymentStatus.paid'),
      [PaymentStatusEnum.FAILED]: t('business:order.paymentStatus.failed'),
      [PaymentStatusEnum.REFUNDED]: t('business:order.paymentStatus.refunded'),
    };
    return statusMap[status] || status;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/business/order')}
            className="p-2"
          >
            <Icon name="arrow-left" size="sm" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              {t('business:order.viewOrder')} #{order.id}
            </h1>
            <p className="text-muted-foreground">
              {t('business:order.createdAt')}: {formatTimestamp(order.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">{t('business:order.status.title')}:</span>
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
              {getOrderStatusLabel(order.orderStatus as OrderStatusEnum)}
            </span>
          </div>
          {order.hasShipping && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">{t('business:order.shippingStatus.title')}:</span>
              <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                {getShippingStatusLabel(order.shippingStatus as ShippingStatusEnum)}
              </span>
            </div>
          )}
        </div>
      </div>

        {/* Thông tin sản phẩm */}
        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-4">{t('business:order.productInfo')}</h2>
            {(order.productInfo as OrderProductInfo)?.products?.map((product: OrderProduct, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg mb-4 last:mb-0">
                <div className="flex-1">
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    Số lượng: {product.quantity} | Đơn giá: {formatCurrency(product.unitPrice)}
                  </p>
                  {product.description && (
                    <p className="text-sm text-muted-foreground mt-1">{product.description}</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-semibold">
                    {formatCurrency(product.totalPrice)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Thông tin thanh toán */}
        <Card>
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-4">{t('business:order.paymentInfo')}</h2>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('business:order.subtotal')}:</span>
                <span className="font-medium">{formatCurrency(order.billInfo?.subtotal || 0)}</span>
              </div>
              {order.billInfo?.shippingFee && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('business:order.shippingFee')}:</span>
                  <span className="font-medium">{formatCurrency(order.billInfo.shippingFee)}</span>
                </div>
              )}
              <div className="flex justify-between border-t pt-3">
                <span className="text-lg font-semibold">{t('business:order.total')}:</span>
                <span className="text-lg font-bold text-primary">
                  {formatCurrency(order.billInfo?.total || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('business:order.paymentMethod')}:</span>
                <span className="font-medium">{order.billInfo?.paymentMethod || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t('business:order.paymentStatus.title')}:</span>
                <span className="px-2 py-1 rounded text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                  {getPaymentStatusLabel(order.billInfo?.paymentStatus as PaymentStatusEnum || PaymentStatusEnum.PENDING)}
                </span>
              </div>
            </div>
          </div>
        </Card>

        {/* Thông tin vận chuyển */}
        {order.hasShipping && order.logisticInfo && (
          <Card>
            <div className="p-6">
              <h2 className="text-lg font-semibold mb-4">{t('business:order.shippingInfo')}</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">{t('business:order.recipient')}:</p>
                  <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo)?.recipientName || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('business:order.phone')}:</p>
                  <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo)?.recipientPhone || 'N/A'}</p>
                </div>
                <div className="md:col-span-2">
                  <p className="text-sm text-muted-foreground">{t('business:order.deliveryAddress')}:</p>
                  <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo)?.deliveryAddress || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('business:order.carrier')}:</p>
                  <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo)?.carrier || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">{t('business:order.trackingNumber')}:</p>
                  <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo)?.trackingNumber || 'N/A'}</p>
                </div>
                {(order.logisticInfo as OrderLogisticInfo)?.estimatedDeliveryTime && (
                  <div className="md:col-span-2">
                    <p className="text-sm text-muted-foreground">{t('business:order.estimatedDelivery')}:</p>
                    <p className="font-medium">
                      {new Date((order.logisticInfo as OrderLogisticInfo).estimatedDeliveryTime!).toLocaleString('vi-VN')}
                    </p>
                  </div>
                )}
                {(order.logisticInfo as OrderLogisticInfo)?.shippingNote && (
                  <div className="md:col-span-2">
                    <p className="text-sm text-muted-foreground">{t('business:order.shippingNote')}:</p>
                    <p className="font-medium">{(order.logisticInfo as OrderLogisticInfo).shippingNote}</p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              console.log('Print order:', order.id);
              // TODO: Implement print functionality
            }}
          >
            <Icon name="printer" size="sm" className="mr-2" />
            {t('business:order.print')}
          </Button>
        </div>
    </div>
  );
};

export default OrderDetailPage;
