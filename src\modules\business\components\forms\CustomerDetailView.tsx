import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, IconCard, Loading, Icon } from '@/shared/components/common';
import { useConvertCustomer } from '../../hooks/useCustomerQuery';
import { UserConvertCustomerListItemDto } from '../../types/customer.types';
import {
  CustomerGeneralInfo,
  CustomerOverview,
  CustomerSocial,
  CustomerCustomFields,
  CustomerInteractions,
  CustomerOrders,
  type CustomerDetailData,
} from './sections';

// Extended interface cho API response với các trường bổ sung
interface ExtendedCustomerApiData extends UserConvertCustomerListItemDto {
  address?: string;
  facebookLink?: string;
  twitterLink?: string;
  linkedinLink?: string;
  zaloLink?: string;
  websiteLink?: string;
  user?: {
    id: number;
    fullName: string;
    email: string;
    phoneNumber: string;
  };
}
interface CustomerDetailViewProps {
  /**
   * ID khách hàng
   */
  customerId: number;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chỉnh sửa
   */
  onEdit?: () => void;
}

/**
 * Function để map dữ liệu từ API response sang CustomerDetailData
 */
const mapToCustomerDetailData = (apiData: ExtendedCustomerApiData): CustomerDetailData => {
  // Extract email string từ object hoặc string
  let emailString = '';
  if (typeof apiData.email === 'string') {
    emailString = apiData.email;
  } else if (apiData.email && typeof apiData.email === 'object' && 'primary' in apiData.email) {
    emailString = apiData.email.primary || '';
  }

  // Xử lý an toàn cho createdAt để tránh lỗi Invalid time value
  const getCustomerSince = (createdAt: unknown): string => {
    try {
      // Kiểm tra nếu createdAt là null, undefined hoặc không hợp lệ
      if (!createdAt) {
        return new Date().toISOString(); // Fallback về ngày hiện tại
      }

      let date: Date;

      // Nếu là số (timestamp)
      if (typeof createdAt === 'number') {
        date = new Date(createdAt);
      }
      // Nếu là string
      else if (typeof createdAt === 'string') {
        // Kiểm tra nếu là timestamp dạng string
        if (/^\d+$/.test(createdAt)) {
          date = new Date(Number(createdAt));
        } else {
          date = new Date(createdAt);
        }
      }
      // Nếu đã là Date object
      else if (createdAt instanceof Date) {
        date = createdAt;
      }
      // Fallback
      else {
        date = new Date();
      }

      // Kiểm tra xem date có hợp lệ không
      if (isNaN(date.getTime())) {
        return new Date().toISOString(); // Fallback về ngày hiện tại
      }

      return date.toISOString();
    } catch (error) {
      console.error('Error parsing createdAt:', error, 'Value:', createdAt);
      return new Date().toISOString(); // Fallback về ngày hiện tại
    }
  };

  return {
    id: String(apiData.id),
    name: apiData.name || '',
    email: emailString,
    phone: apiData.phone || '',
    address: apiData.address || '', // Địa chỉ từ API response
    ...(apiData.avatar && { avatar: apiData.avatar }),
    tags: apiData.platform ? [apiData.platform] : [], // Tags từ platform
    status: 'active', // Default status
    totalOrders: 0, // Default values - có thể cần API khác để lấy
    totalSpent: 0,
    averageOrderValue: 0,
    // lastOrderDate: chưa có trong API - bỏ để tránh lỗi exactOptionalPropertyTypes
    customerSince: getCustomerSince(apiData.createdAt),
    // Overview data - default values với một số mock data
    flowCount: 2,
    campaignCount: 1,
    sequenceCount: 3,
    topChannels: apiData.platform ? [
      {
        id: '1',
        name: apiData.platform,
        count: 5,
        percentage: 100,
        icon: apiData.platform.toLowerCase(),
      }
    ] : [],
    topDevices: [
      {
        id: '1',
        name: 'Desktop',
        count: 3,
        percentage: 60,
        icon: 'monitor',
      },
      {
        id: '2',
        name: 'Mobile',
        count: 2,
        percentage: 40,
        icon: 'smartphone',
      }
    ],
    // Social profiles từ API response
    socialProfiles: {
      ...(apiData.facebookLink && { facebook: apiData.facebookLink }),
      ...(apiData.twitterLink && { twitter: apiData.twitterLink }),
      ...(apiData.linkedinLink && { linkedin: apiData.linkedinLink }),
      ...(apiData.zaloLink && { zalo: apiData.zaloLink }),
      ...(apiData.websiteLink && { website: apiData.websiteLink }),
    },
    customFields: apiData.metadata || {},
    // Mock data cho interactions
    interactions: [],
    // Mock data cho orders
    orders: [],
    // Mock data cho activities
    activities: [],
  };
};

/**
 * Component hiển thị chi tiết khách hàng
 */
const CustomerDetailView: React.FC<CustomerDetailViewProps> = ({ customerId, onClose, onEdit }) => {
  const { t } = useTranslation(['business', 'common']);

  // Sử dụng hook useConvertCustomer để lấy dữ liệu khách hàng
  const { data: apiCustomer, isLoading, error } = useConvertCustomer(customerId);

  // Map dữ liệu từ API sang format mà components expect
  const customer = apiCustomer ? mapToCustomerDetailData(apiCustomer as ExtendedCustomerApiData) : null;

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="alert-circle" className="w-16 h-16 text-red-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('business:customer.loadError')}
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            {error.message}
          </Typography>
        </div>
      </div>
    );
  }

  // Not found state
  if (!customer) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="user" className="w-16 h-16 text-gray-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-gray-500 mb-2">
            {t('business:customer.notFound')}
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {t('business:customer.notFoundDescription')}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <Typography variant="h4" className="text-foreground">
            {t('business:customer.detailForm')}
          </Typography>
          <Typography variant="body2" className="text-muted mt-1">
            {customer.name}
          </Typography>
        </div>
        <div className="flex space-x-3">
          {onEdit && (
            <IconCard
              icon="edit"
              variant="secondary"
              size="md"
              title={t('common:edit')}
              onClick={onEdit}
            />
          )}
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:close')}
            onClick={onClose}
          />
        </div>
      </div>

      {/* Thông tin chung */}
      <CustomerGeneralInfo customer={customer} />

      {/* Trường tùy chỉnh */}
      <CustomerCustomFields customer={customer} />

      {/* Tổng quan */}
      <CustomerOverview customer={customer} />

      {/* Social */}
      <CustomerSocial customer={customer} />

      {/* Tương tác */}
      <CustomerInteractions customer={customer} />

      {/* Đơn hàng */}
      <CustomerOrders customer={customer} />
    </div>
  );
};

export default CustomerDetailView;
