/**
 * Types cho API địa chỉ Việt Nam từ provinces.open-api.vn
 */

export interface Province {
  code: number;
  name: string;
  name_en?: string;
  full_name?: string;
  full_name_en?: string;
  code_name?: string;
  administrative_unit_id?: number;
  administrative_region_id?: number;
  districts?: District[];
}

export interface District {
  code: number;
  name: string;
  name_en?: string;
  full_name?: string;
  full_name_en?: string;
  code_name?: string;
  province_code: number;
  administrative_unit_id?: number;
  wards?: Ward[];
}

export interface Ward {
  code: number;
  name: string;
  name_en?: string;
  full_name?: string;
  full_name_en?: string;
  code_name?: string;
  district_code: number;
  administrative_unit_id?: number;
}

export interface AdministrativeUnit {
  id: number;
  full_name: string;
  full_name_en: string;
  short_name: string;
  short_name_en: string;
  code_name: string;
  code_name_en: string;
}

export interface AdministrativeRegion {
  id: number;
  name: string;
  name_en: string;
  code_name: string;
  code_name_en: string;
}

/**
 * Response từ API provinces.open-api.vn
 */
export interface ProvinceApiResponse {
  results: Province[];
}

export interface DistrictApiResponse {
  results: District[];
}

export interface WardApiResponse {
  results: Ward[];
}

/**
 * Options cho Select component
 */
export interface AddressSelectOption {
  value: string;
  label: string;
  code?: number;
}
