import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Divider, IconCard, Avatar, Loading, Grid } from '@/shared/components/common';
import { useConversion } from '../../hooks/useConversionQuery';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

interface ViewConversionFormProps {
  id: number;
  onClose: () => void;
}

/**
 * Form xem chi tiết chuyển đổi
 */
const ViewConversionForm: React.FC<ViewConversionFormProps> = ({
  id,
  onClose,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { data: conversion, isLoading } = useConversion(id);

  if (isLoading) {
    return (
     <Loading/>
    );
  }

  if (!conversion) {
    return (
      <Card title={t('admin:business.conversion.detail')}>
        <div className="p-6">
          <Typography variant="body1" color="muted">
            {t('admin:business.conversion.notFound')}
          </Typography>
        </div>
      </Card>
    );
  }

  const formatTimestamp = (timestamp: number) => {
    try {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, 'dd/MM/yyyy HH:mm', { locale: vi });
      }
      return String(timestamp);
    } catch {
      return String(timestamp);
    }
  };

  const renderEmailInfo = (email: unknown) => {
    if (!email) return '-';

    if (typeof email === 'string') {
      return email;
    }

    if (typeof email === 'object' && email !== null) {
      const emailObj = email as Record<string, string>;
      return (
        <div className="space-y-1">
          {emailObj['primary'] && (
            <div>

              <Typography variant="body2" className="ml-2 inline">
                {emailObj['primary']}
              </Typography>
            </div>
          )}

        </div>
      );
    }

    return String(email);
  };

  // const renderTags = (tags: string[]) => {
  //   if (!tags || tags.length === 0) return '-';

  //   return (
  //     <div className="flex flex-wrap gap-2">
  //       {tags.map((tag, index) => (
  //         <Badge key={index} variant="secondary">
  //           {tag}
  //         </Badge>
  //       ))}
  //     </div>
  //   );
  // };

  const renderContentField = (key: string, value: unknown) => {
    if (value === null || value === undefined) return null;

    let displayValue: string;
    if (typeof value === 'object') {
      displayValue = JSON.stringify(value, null, 2);
    } else {
      displayValue = String(value);
    }

    return (
      <div key={key} className="space-y-1">
        <Typography variant="body2" color="muted">
          {key}:
        </Typography>
        <div className="text-sm bg-muted/50 p-3 rounded-lg">
          {typeof value === 'object' ? (
            <pre className="whitespace-pre-wrap text-foreground">
              {displayValue}
            </pre>
          ) : (
            <Typography variant="body2">{displayValue}</Typography>
          )}
        </div>
      </div>
    );
  };

  return (
    <Card title={t('admin:business.conversion.detail')}>
      <div className="p-6 space-y-6">
        {/* Thông tin chuyển đổi cơ bản */}
        <div className="space-y-4">
          <Typography variant="h6" color="primary">
            {t('admin:business.conversion.conversionInfo')}
          </Typography>

          <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                ID:
              </Typography>
              <Typography variant="body2">{conversion.id}</Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.customerId')}:
              </Typography>
              <Typography variant="body2">{conversion.convertCustomerId}</Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.userId')}:
              </Typography>
              <Typography variant="body2">{conversion.userId}</Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.conversionType')}:
              </Typography>
              <Typography variant="body2">{conversion.conversionType}</Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.source')}:
              </Typography>
              <Typography variant="body2">{conversion.source}</Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.createdAt')}:
              </Typography>
              <Typography variant="body2">
                {formatTimestamp(conversion.createdAt)}
              </Typography>
            </div>

            <div className="flex items-center gap-x-3">
              <Typography variant="body2" color="muted" className="min-w-[120px]">
                {t('admin:business.conversion.updatedAt')}:
              </Typography>
              <Typography variant="body2">
                {formatTimestamp(conversion.updatedAt)}
              </Typography>
            </div>
          </Grid>

          {conversion.notes && (
            <div className="space-y-2">
              <Typography variant="body2" color="muted">
                {t('admin:business.conversion.notes')}:
              </Typography>
              <div className="bg-muted/50 p-3 rounded-lg">
                <Typography variant="body2">{conversion.notes}</Typography>
              </div>
            </div>
          )}
        </div>

        <Divider />

        {/* Thông tin khách hàng */}
        {conversion.customer && (
          <div className="space-y-4">
            <Typography variant="h6" color="primary">
              {t('admin:business.conversion.customerInfo')}
            </Typography>

            <div className="flex items-start gap-4">
              {conversion.customer.avatar && (
                <Avatar
                  src={conversion.customer.avatar}
                  alt={conversion.customer.name}
                  size="lg"
                />
              )}

              <div className="flex-1 space-y-3">
                <Grid columns={{ xs: 1, md: 2 }} columnGap="md" rowGap="sm">
                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      ID:
                    </Typography>
                    <Typography variant="body2">{conversion.customer.id}</Typography>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.name')}:
                    </Typography>
                    <Typography variant="body2" weight="medium">
                      {conversion.customer.name}
                    </Typography>
                  </div>

                  <div className="flex items-start gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.email')}:
                    </Typography>
                    <div className="flex-1">
                      {renderEmailInfo(conversion.customer.email)}
                    </div>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.phone')}:
                    </Typography>
                    <Typography variant="body2">
                      {conversion.customer.phone || '-'}
                    </Typography>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.platform')}:
                    </Typography>
                    <Typography variant="body2">
                      {conversion.customer.platform || '-'}
                    </Typography>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.timezone')}:
                    </Typography>
                    <Typography variant="body2">
                      {conversion.customer.timezone || '-'}
                    </Typography>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.agentId')}:
                    </Typography>
                    <Typography variant="body2">
                      {conversion.customer.agentId || '-'}
                    </Typography>
                  </div>

                  <div className="flex items-center gap-x-3">
                    <Typography variant="body2" color="muted" className="min-w-[120px]">
                      {t('admin:business.conversion.createdAt')}:
                    </Typography>
                    <Typography variant="body2">
                      {formatTimestamp(conversion.customer.createdAt)}
                    </Typography>
                  </div>

                  {conversion.customer.updatedAt && (
                    <div className="flex items-center gap-x-3">
                      <Typography variant="body2" color="muted" className="min-w-[120px]">
                        {t('admin:business.conversion.updatedAt')}:
                      </Typography>
                      <Typography variant="body2">
                        {formatTimestamp(conversion.customer.updatedAt)}
                      </Typography>
                    </div>
                  )}
                </Grid>


              </div>
            </div>
          </div>
        )}

        <Divider />

        {/* Nội dung chuyển đổi */}
        {conversion.content && Object.keys(conversion.content).length > 0 && (
          <div className="space-y-4">
            <Typography variant="h6" color="primary">
              {t('admin:business.conversion.content')}
            </Typography>

            <div className="space-y-4">
              {Object.entries(conversion.content).map(([key, value]) =>
                renderContentField(key, value)
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end pt-4">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('admin:business.conversion.close')}
            onClick={onClose}
          />
        </div>
      </div>
    </Card>
  );
};

export default ViewConversionForm;
