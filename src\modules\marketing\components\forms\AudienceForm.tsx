import React, { useState, useCallback } from 'react';
import { Card, Form, FormItem, Input, IconCard, Typography, TagsSelectWithPagination, PhoneInputWithCountry, CollapsibleCard } from '@/shared/components/common';
import SimpleCustomFieldSelector from '@/modules/business/components/SimpleCustomFieldSelector';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { z } from 'zod';

// Schema cho form - chỉ validate các field trong Form component
const formSchema = z.object({
  name: z.string().min(1, 'Tên đối tượng là bắt buộc'),
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Email không hợp lệ'
    }),
});

export type AudienceFormValues = z.infer<typeof formSchema>;

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

interface AudienceFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa đối tượng
 */
const AudienceForm: React.FC<AudienceFormProps> = ({ onSubmit, onCancel }) => {
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [phoneValue, setPhoneValue] = useState<string>('');
  const [countryCode, setCountryCode] = useState<string>('+84');
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);

  const handlePhoneChange = (phone: string, country: string) => {
    setPhoneValue(phone);
    setCountryCode(country);
  };

  // Thêm/xóa trường tùy chỉnh vào audience
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setAudienceCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Thêm trường mới với thông tin đầy đủ
        const newField: SelectedCustomField = {
          id: Date.now(), // ID tạm thời
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
          type: (fieldData?.['type'] as string) || 'text',
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi audience
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong audience
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setAudienceCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('AudienceForm handleSubmit called with:', values); // Debug log
    console.log('Phone value:', phoneValue); // Debug log
    console.log('Country code:', countryCode); // Debug log
    console.log('Selected tags:', selectedTags); // Debug log

    // Thêm selectedTags, phone, countryCode và customFields vào values
    const formData = {
      ...values,
      ...(phoneValue && phoneValue.trim() && { phone: phoneValue }),
      ...(countryCode && { countryCode: countryCode }),
      tagIds: selectedTags,
      customFields: audienceCustomFields.map(field => ({
        fieldId: field.fieldId,
        configId: field.configJson?.['configId'] || field.fieldId.toString(),
        value: field.value['value'],
      })),
    };

    console.log('Final form data:', formData); // Debug log
    onSubmit(formData);
  };

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        Thêm đối tượng mới
      </Typography>

      <Form schema={formSchema} onSubmit={handleSubmit} submitOnEnter={false} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label="Tên đối tượng" required>
            <Input placeholder="Nhập tên đối tượng" fullWidth />
          </FormItem>

          <FormItem name="email" label="Email">
            <Input type="email" placeholder="Nhập email" fullWidth />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Số điện thoại
            </label>
            <PhoneInputWithCountry
              value={phoneValue}
              onChange={handlePhoneChange}
              placeholder="Nhập số điện thoại"
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Tags</label>
            <TagsSelectWithPagination
              value={selectedTags}
              onChange={setSelectedTags}
              placeholder="Chọn tags..."
              fullWidth
            />
          </div>
        </div>

        {/* 7. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              Trường tùy chỉnh
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={audienceCustomFields.map(f => f.fieldId)}
              placeholder="Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh..."
            />

            {audienceCustomFields.length > 0 && (
              <div className="space-y-3">
                {audienceCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={(field.value['value'] as string) || ''}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value as string)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <IconCard
            icon="x"
            variant="default"
            size="md"
            title="Hủy"
            onClick={onCancel}
          />
          <IconCard
            icon="save"
            variant="default"
            size="md"
            title="Lưu"
            onClick={() => {
              // Trigger form submit programmatically
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
          />
        </div>
      </Form>
    </Card>
  );
};

export default AudienceForm;
