/**
 * Hooks cho Admin Marketing Custom Fields - hoàn toàn độc lập
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  AdminMarketingCustomFieldBusinessService,
  AdminMarketingCustomFieldService,
  MarketingCustomFieldQueryParams,
} from '../services/custom-field.service';
import {
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
} from '../types/custom-field.types';

/**
 * Query keys cho admin marketing custom fields
 */
export const ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS = {
  all: ['admin', 'marketing', 'custom-fields'] as const,
  lists: () => [...ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'list'] as const,
  list: (params: MarketingCustomFieldQueryParams) => [...ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.details(), id] as const,
  configExamples: () => [...ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'config-examples'] as const,
};

/**
 * Hook lấy danh sách admin marketing custom fields
 */
export const useAdminMarketingCustomFields = (params: MarketingCustomFieldQueryParams = {}) => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useQuery({
    queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: () => AdminMarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(params),
    select: (data) => {
      // API trả về { code, message, result: { items, meta } }
      return data.result || data.data || data;
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error fetching admin marketing custom fields:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketingAdmin:customField.errors.fetchError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook lấy chi tiết admin marketing custom field
 */
export const useAdminMarketingCustomField = (id: number) => {
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useQuery({
    queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => AdminMarketingCustomFieldService.getCustomFieldById(id),
    select: (data) => data.data || data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error fetching admin marketing custom field detail:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketingAdmin:customField.errors.fetchDetailError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook tạo admin marketing custom field mới
 */
export const useCreateAdminMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useMutation({
    mutationFn: (data: CreateMarketingCustomFieldRequest) =>
      AdminMarketingCustomFieldBusinessService.createCustomFieldWithValidation(data),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketingAdmin:customField.createSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error creating admin marketing custom field:', error);
      
      let errorMessage = t('marketingAdmin:customField.errors.createError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook cập nhật admin marketing custom field
 */
export const useUpdateAdminMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateMarketingCustomFieldRequest }) =>
      AdminMarketingCustomFieldBusinessService.updateCustomFieldWithValidation(id, data),
    onSuccess: (data, variables) => {
      NotificationUtil.success({
        message: data.message || t('marketingAdmin:customField.updateSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách và chi tiết
      queryClient.invalidateQueries({
        queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
      
      queryClient.invalidateQueries({
        queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(variables.id),
        exact: true,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error updating admin marketing custom field:', error);
      
      let errorMessage = t('marketingAdmin:customField.errors.updateError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook xóa admin marketing custom field
 */
export const useDeleteAdminMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useMutation({
    mutationFn: (id: number) => AdminMarketingCustomFieldService.deleteCustomField(id),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketingAdmin:customField.deleteSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error deleting admin marketing custom field:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketingAdmin:customField.errors.deleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa nhiều admin marketing custom fields
 */
export const useDeleteMultipleAdminMarketingCustomFields = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketingAdmin', 'common']);

  return useMutation({
    mutationFn: (ids: number[]) => AdminMarketingCustomFieldService.deleteMultipleCustomFields(ids),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketingAdmin:customField.deleteMultipleSuccess', { count: data.data?.deletedCount || 0 }),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: ADMIN_MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error deleting multiple admin marketing custom fields:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketingAdmin:customField.errors.deleteMultipleError'),
        duration: 3000,
      });
    },
  });
};
