import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Input,
  Textarea,
  DateTimePicker,
  Select,
} from '@/shared/components/common';
import type {
  EventDeliveryInfo,
  EventDeliveryFormProps
} from '../../types/delivery.types';

/**
 * Form thông tin sự kiện
 */
const EventDeliveryForm: React.FC<EventDeliveryFormProps> = ({
  eventInfo,
  onEventInfoChange,
  customerName,
  customerPhone,
  customerEmail,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Helper function to create clean EventDeliveryInfo object
  const createUpdatedInfo = (updates: Partial<EventDeliveryInfo>): EventDeliveryInfo => {
    const updatedInfo: EventDeliveryInfo = {};

    // Copy existing properties that are not undefined
    if (eventInfo?.eventDate !== undefined) updatedInfo.eventDate = eventInfo.eventDate;
    if (eventInfo?.eventTime !== undefined) updatedInfo.eventTime = eventInfo.eventTime;
    if (eventInfo?.eventLocation !== undefined) updatedInfo.eventLocation = eventInfo.eventLocation;
    if (eventInfo?.eventAddress !== undefined) updatedInfo.eventAddress = eventInfo.eventAddress;
    if (eventInfo?.ticketDeliveryMethod !== undefined) updatedInfo.ticketDeliveryMethod = eventInfo.ticketDeliveryMethod;
    if (eventInfo?.ticketRecipient !== undefined) updatedInfo.ticketRecipient = eventInfo.ticketRecipient;
    if (eventInfo?.eventNotes !== undefined) updatedInfo.eventNotes = eventInfo.eventNotes;
    if (eventInfo?.attendeeName !== undefined) updatedInfo.attendeeName = eventInfo.attendeeName;
    if (eventInfo?.attendeePhone !== undefined) updatedInfo.attendeePhone = eventInfo.attendeePhone;
    if (eventInfo?.attendeeEmail !== undefined) updatedInfo.attendeeEmail = eventInfo.attendeeEmail;

    // Apply updates, filtering out undefined values
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        (updatedInfo as Record<string, unknown>)[key] = value;
      }
    });

    return updatedInfo;
  };

  // Xử lý thay đổi ngày sự kiện
  const handleEventDateChange = (date: Date | null) => {
    const updates: Partial<EventDeliveryInfo> = {};
    if (date) {
      updates.eventDate = date.toISOString();
    }
    onEventInfoChange(createUpdatedInfo(updates));
  };

  // Xử lý thay đổi địa điểm sự kiện
  const handleLocationChange = (eventLocation: string) => {
    onEventInfoChange(createUpdatedInfo({ eventLocation }));
  };

  // Xử lý thay đổi địa chỉ sự kiện
  const handleAddressChange = (eventAddress: string) => {
    onEventInfoChange(createUpdatedInfo({ eventAddress }));
  };

  // Xử lý thay đổi phương thức giao vé
  const handleTicketDeliveryMethodChange = (method: string | number | string[] | number[]) => {
    const methodStr = Array.isArray(method) ? method[0]?.toString() : method?.toString();

    // Ensure we have a valid method string
    if (!methodStr) return;

    let defaultRecipient = '';

    // Tự động điền recipient dựa trên phương thức
    switch (methodStr) {
      case 'EMAIL':
        defaultRecipient = customerEmail || '';
        break;
      case 'SMS':
        defaultRecipient = customerPhone || '';
        break;
      default:
        defaultRecipient = eventInfo?.ticketRecipient || '';
    }

    // Validate that methodStr is a valid ticket delivery method
    const validMethods = ['EMAIL', 'SMS', 'PICKUP', 'MAIL'] as const;
    type ValidMethod = typeof validMethods[number];
    const validMethod = validMethods.includes(methodStr as ValidMethod)
      ? methodStr as ValidMethod
      : 'EMAIL' as const;

    onEventInfoChange(createUpdatedInfo({
      ticketDeliveryMethod: validMethod,
      ticketRecipient: defaultRecipient,
    }));
  };

  // Xử lý thay đổi người nhận vé
  const handleTicketRecipientChange = (ticketRecipient: string) => {
    onEventInfoChange(createUpdatedInfo({ ticketRecipient }));
  };

  // Xử lý thay đổi ghi chú sự kiện
  const handleNotesChange = (eventNotes: string) => {
    onEventInfoChange(createUpdatedInfo({ eventNotes }));
  };

  // Xử lý thay đổi tên người tham dự
  const handleAttendeeNameChange = (attendeeName: string) => {
    onEventInfoChange(createUpdatedInfo({ attendeeName }));
  };

  // Xử lý thay đổi số điện thoại người tham dự
  const handleAttendeePhoneChange = (attendeePhone: string) => {
    onEventInfoChange(createUpdatedInfo({ attendeePhone }));
  };

  // Xử lý thay đổi email người tham dự
  const handleAttendeeEmailChange = (attendeeEmail: string) => {
    onEventInfoChange(createUpdatedInfo({ attendeeEmail }));
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('business:order.eventDelivery.title')}
        </Typography>
        
        {/* Thông tin sự kiện */}
        <div className="mb-6">
          <Typography variant="subtitle1" className="mb-3">
            {t('business:order.eventDelivery.eventInfo')}
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:order.eventDelivery.eventDate')}>
              <DateTimePicker
                value={eventInfo?.eventDate ? new Date(eventInfo.eventDate) : null}
                onChange={handleEventDateChange}
                placeholder={t('business:order.eventDelivery.eventDatePlaceholder')}
                fullWidth
              />
            </FormItem>
            
            <FormItem label={t('business:order.eventDelivery.eventLocation')}>
              <Input
                value={eventInfo?.eventLocation || ''}
                onChange={(e) => handleLocationChange(e.target.value)}
                placeholder={t('business:order.eventDelivery.eventLocationPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>

          <FormItem label={t('business:order.eventDelivery.eventAddress')}>
            <Input
              value={eventInfo?.eventAddress || ''}
              onChange={(e) => handleAddressChange(e.target.value)}
              placeholder={t('business:order.eventDelivery.eventAddressPlaceholder')}
              fullWidth
            />
          </FormItem>
        </div>

        {/* Thông tin giao vé */}
        <div className="mb-6">
          <Typography variant="subtitle1" className="mb-3">
            {t('business:order.eventDelivery.ticketDelivery')}
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:order.eventDelivery.ticketDeliveryMethod')} required>
              <Select
                value={eventInfo?.ticketDeliveryMethod || 'EMAIL'}
                onChange={handleTicketDeliveryMethodChange}
                options={[
                  { 
                    value: 'EMAIL', 
                    label: t('business:order.eventDelivery.ticketMethods.email') 
                  },
                  { 
                    value: 'SMS', 
                    label: t('business:order.eventDelivery.ticketMethods.sms') 
                  },
                  { 
                    value: 'PICKUP', 
                    label: t('business:order.eventDelivery.ticketMethods.pickup') 
                  },
                  { 
                    value: 'MAIL', 
                    label: t('business:order.eventDelivery.ticketMethods.mail') 
                  },
                ]}
                fullWidth
              />
            </FormItem>
            
            <FormItem label={t('business:order.eventDelivery.ticketRecipient')} required>
              <Input
                value={eventInfo?.ticketRecipient || ''}
                onChange={(e) => handleTicketRecipientChange(e.target.value)}
                placeholder={
                  eventInfo?.ticketDeliveryMethod === 'EMAIL' 
                    ? t('business:order.eventDelivery.emailPlaceholder')
                    : eventInfo?.ticketDeliveryMethod === 'SMS'
                    ? t('business:order.eventDelivery.phonePlaceholder')
                    : t('business:order.eventDelivery.recipientPlaceholder')
                }
                fullWidth
              />
            </FormItem>
          </div>
        </div>

        {/* Thông tin người tham dự */}
        <div className="mb-6">
          <Typography variant="subtitle1" className="mb-3">
            {t('business:order.eventDelivery.attendeeInfo')}
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormItem label={t('business:order.eventDelivery.attendeeName')}>
              <Input
                value={eventInfo?.attendeeName || customerName || ''}
                onChange={(e) => handleAttendeeNameChange(e.target.value)}
                placeholder={t('business:order.eventDelivery.attendeeNamePlaceholder')}
                fullWidth
              />
            </FormItem>
            
            <FormItem label={t('business:order.eventDelivery.attendeePhone')}>
              <Input
                value={eventInfo?.attendeePhone || customerPhone || ''}
                onChange={(e) => handleAttendeePhoneChange(e.target.value)}
                placeholder={t('business:order.eventDelivery.attendeePhonePlaceholder')}
                fullWidth
              />
            </FormItem>
            
            <FormItem label={t('business:order.eventDelivery.attendeeEmail')}>
              <Input
                value={eventInfo?.attendeeEmail || customerEmail || ''}
                onChange={(e) => handleAttendeeEmailChange(e.target.value)}
                placeholder={t('business:order.eventDelivery.attendeeEmailPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>
        </div>
        
        <FormItem label={t('business:order.eventDelivery.eventNotes')}>
          <Textarea
            value={eventInfo?.eventNotes || ''}
            onChange={(e) => handleNotesChange(e.target.value)}
            placeholder={t('business:order.eventDelivery.eventNotesPlaceholder')}
            rows={4}
            fullWidth
          />
        </FormItem>

        {/* Thông tin hướng dẫn */}
        <div className="mt-4 p-4 bg-green-50 rounded-lg">
          <Typography variant="subtitle2" className="mb-2 text-green-800">
            {t('business:order.eventDelivery.instructions')}
          </Typography>
          <Typography variant="body2" className="text-green-600">
            {t('business:order.eventDelivery.instructionsText')}
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default EventDeliveryForm;
