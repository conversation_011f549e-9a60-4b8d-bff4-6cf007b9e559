import { useNavigate } from 'react-router-dom';
import { Card, Button, Typography } from '@/shared/components/common';
import TestSegmentForm from '../components/forms/TestSegmentForm';
import { useState } from 'react';

/**
 * Trang test để kiểm tra giao diện Marketing
 */
export function MarketingTestPage() {
  const navigate = useNavigate();
  const [showAsyncSelectTest, setShowAsyncSelectTest] = useState(false);

  const testPages = [
    {
      title: 'Marketing Dashboard',
      description: 'Trang tổng quan marketing',
      path: '/marketing/dashboard',
    },
    {
      title: 'Zalo Overview',
      description: 'Tổng quan Zalo Marketing',
      path: '/marketing/zalo/overview',
    },
    {
      title: 'Zalo Accounts',
      description: 'Quản lý Zalo OA',
      path: '/marketing/zalo/accounts',
    },
    {
      title: 'Zalo ZNS Templates',
      description: 'Quản lý ZNS Templates',
      path: '/marketing/zalo/zns',
    },
    {
      title: 'Email Overview',
      description: 'Tổng quan Email Marketing',
      path: '/marketing/email/overview',
    },
    {
      title: 'Email Templates',
      description: 'Quản lý Email Templates',
      path: '/marketing/email/templates',
    },
  ];

  if (showAsyncSelectTest) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Typography variant="h4">AsyncSelectWithPagination Test</Typography>
          <Button
            variant="outline"
            onClick={() => setShowAsyncSelectTest(false)}
          >
            ← Back to Test Menu
          </Button>
        </div>
        <TestSegmentForm />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Test AsyncSelectWithPagination Button */}
      <Card title="Component Tests">
        <div className="space-y-4">
          <Button
            variant="primary"
            onClick={() => setShowAsyncSelectTest(true)}
            className="w-full"
          >
            🧪 Test AsyncSelectWithPagination - Selected Value Display
          </Button>
          <Typography variant="body2" className="text-muted-foreground">
            Test để kiểm tra việc hiển thị giá trị đã chọn trong AsyncSelectWithPagination component
          </Typography>
        </div>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {testPages.map((page) => (
          <Card
            key={page.path}
            title={page.title}
            subtitle={page.description}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => navigate(page.path)}
            hoverable
          >
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(page.path);
                }}
              >
                Xem trang
              </Button>
            </div>
          </Card>
        ))}
      </div>

      <Card title="Thông tin Module">
        <div className="space-y-2 text-sm">
          <p><strong>Phase:</strong> 1 & 2 - Foundation & Core Features</p>
          <p><strong>Status:</strong> ✅ Hoàn thành</p>
          <p><strong>Features:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>Zalo Marketing: OA management, Followers, ZNS Templates</li>
            <li>Email Marketing: Templates với CRUD operations</li>
            <li>Dashboard tổng quan multi-channel</li>
            <li>Responsive design & TypeScript strict mode</li>
          </ul>
        </div>
      </Card>
    </div>
  );
}

export default MarketingTestPage;
