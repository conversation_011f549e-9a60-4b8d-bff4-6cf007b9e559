import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
// Tạm thời tắt vite-plugin-eslint do xung đột với ESLint v9
// @ts-ignore
// import eslint from 'vite-plugin-eslint';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import checker from 'vite-plugin-checker';

// https://vite.dev/config/
export default defineConfig({
  server: {
    host: true,
    port: 5173,
    cors: true,
    proxy: {},
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
    open: false, // Không tự động mở browser để tránh conflict
    hmr: {
      host: 'localhost',
      overlay: true, // Hiển thị lỗi trên overlay
    },
    watch: {
      usePolling: false, // Tắt polling để cải thiện hiệu suất HMR
      interval: 100, // Giảm interval xuống 100ms để phản hồi n<PERSON>h hơn
      ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**'], // Bỏ qua các thư mục không cần thiết
    },
  },
  preview: {
    host: true,
    port: 5173,
    strictPort: false,
    allowedHosts: ['localhost', '127.0.0.1', 'v2.redai.vn', '*************'],
  },
  build: {
    chunkSizeWarningLimit: 1000, // Tăng giới hạn cảnh báo lên 1000kb
    // Không cho phép build nếu có lỗi TypeScript
    minify: true,
    sourcemap: true,
    reportCompressedSize: true,
    commonjsOptions: {
      // Đảm bảo các module CommonJS được xử lý đúng cách
      transformMixedEsModules: true,
      include: [/node_modules/],
    },
    rollupOptions: {
      external: [],
      output: {
        // Bỏ manualChunks để Vite tự động xử lý việc phân chia code
        // Điều này sẽ đảm bảo React và tất cả API của nó được tải đúng thứ tự
        manualChunks: undefined,
      },
    },
  },
  plugins: [
    react({
      // Cấu hình plugin React để đảm bảo React được xử lý đúng cách
      jsxRuntime: 'automatic',
      // Fast Refresh is enabled by default in @vitejs/plugin-react v4+
      babel: {
        plugins: [
          ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
        ]
      }
    }),
    // Tạm thời tắt vite-plugin-eslint do xung đột với ESLint v9
    // eslint({
    //   failOnError: false, // Không dừng quá trình build nếu có lỗi ESLint
    //   failOnWarning: false, // Không dừng quá trình build nếu chỉ có cảnh báo
    //   include: ['src/**/*.ts', 'src/**/*.tsx'], // Các file cần kiểm tra
    //   exclude: ['node_modules/**', 'dist/**'], // Loại trừ các thư mục không cần thiết
    //   cache: false, // Tắt cache để đảm bảo luôn kiểm tra mới nhất
    //   emitWarning: true, // Hiển thị warning
    //   emitError: true, // Hiển thị error
    // }),
    // Plugin checker để hiển thị lỗi TypeScript trong giao diện
    // ESLint tạm thời tắt trong checker do xung đột với ESLint v9
    checker({
      typescript: {
        tsconfigPath: './tsconfig.app.json', // Đường dẫn đến file tsconfig
        root: './', // Thư mục gốc của dự án
        buildMode: false, // Tắt kiểm tra trong quá trình build để tăng tốc HMR
      },
      overlay: {
        initialIsOpen: true, // Hiển thị overlay khi có lỗi TypeScript
        position: 'tl', // Vị trí top-left
        badgeStyle: 'position: fixed; top: 10px; left: 10px; z-index: 9999;', // Style cho badge
      },
      enableBuild: false, // Tắt kiểm tra khi build để tăng tốc
      terminal: true, // Hiển thị lỗi trong terminal
    }),
    visualizer({
      open: true, // Tự động mở báo cáo sau khi build
      filename: 'dist/stats.html', // Đường dẫn lưu báo cáo
      gzipSize: true, // Hiển thị kích thước gzip
      brotliSize: true, // Hiển thị kích thước brotli
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve('./src'),
      '@components': path.resolve('./src/components'),
      '@hooks': path.resolve('./src/hooks'),
      '@contexts': path.resolve('./src/contexts'),
      '@layouts': path.resolve('./src/layouts'),
      '@pages': path.resolve('./src/pages'),
      '@services': path.resolve('./src/services'),
      '@store': path.resolve('./src/store'),
      '@styles': path.resolve('./src/styles'),
      '@types': path.resolve('./src/types'),
      '@lib': path.resolve('./src/lib'),
      '@constants': path.resolve('./src/constants'),
      '@assets': path.resolve('./src/assets'),
    },
  },
});